// src/app.ts

// Umi a-pp runtime configuration
// This file is required by the initialState plugin.

/**
 * @see  https://umijs.org/docs/max/layout-menu#getinitialstate
 * */
export async function getInitialState(): Promise<{ name: string }> {
  // This function is called when the app initializes.
  // You can fetch user info, permissions, etc. here.
  // For now, we'll return a mock user.
  console.log('getInitialState triggered');
  return { name: '@guest' };
}
