// @ts-nocheck
// This file is generated by <PERSON><PERSON> automatically
// DO NOT CHANGE IT MANUALLY!
import routeProps from './routeProps';

if (process.env.NODE_ENV === 'development') {
  Object.entries(routeProps).forEach(([key, value]) => {
    const internalProps = ['path', 'id', 'parentId', 'isLayout', 'isWrapper', 'layout', 'clientLoader'];
    Object.keys(value).forEach((prop) => {
      if (internalProps.includes(prop)) {
        throw new Error(
          `[UmiJS] route '${key}' should not have '${prop}' prop, please remove this property in 'routeProps'.`
        )
      }
    })
  })
}

import React from 'react';

export async function getRoutes() {
  const routes = {"admin/Dashboard":{"path":"admin/Dashboard","id":"admin/Dashboard","parentId":"@@/global-layout"},"display/about":{"path":"display/about","id":"display/about","parentId":"@@/global-layout"},"display/index":{"path":"display","id":"display/index","parentId":"@@/global-layout"},"admin/Login":{"path":"admin/Login","id":"admin/Login","parentId":"@@/global-layout"},"index":{"path":"/","id":"index","parentId":"@@/global-layout"},"@@/global-layout":{"id":"@@/global-layout","path":"/","isLayout":true}} as const;
  return {
    routes,
    routeComponents: {
'admin/Dashboard': React.lazy(() => import(/* webpackChunkName: "src__pages__admin__Dashboard" */'../../../src/pages/admin/Dashboard.tsx')),
'display/about': React.lazy(() => import(/* webpackChunkName: "src__pages__display__about" */'../../../src/pages/display/about.tsx')),
'display/index': React.lazy(() => import(/* webpackChunkName: "src__pages__display__index" */'../../../src/pages/display/index.tsx')),
'admin/Login': React.lazy(() => import(/* webpackChunkName: "src__pages__admin__Login" */'../../../src/pages/admin/Login.tsx')),
'index': React.lazy(() => import(/* webpackChunkName: "src__pages__index" */'../../../src/pages/index.tsx')),
'@@/global-layout': React.lazy(() => import(/* webpackChunkName: "layouts__index" */'/Users/<USER>/WebstormProjects/inCODE/ui/src/layouts/index.tsx')),
},
  };
}
