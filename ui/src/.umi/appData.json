{"cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "pkg": {"name": "ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "umi dev", "build": "umi build", "setup": "umi setup", "start": "npm run dev"}, "dependencies": {"@umijs/plugins": "^4.4.2", "ahooks": "^3.8.0", "antd": "^5.21.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^10.1.0", "umi": "^4.4.2"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "typescript": "^5.0.2", "vite": "^4.4.5"}}, "pkgPath": "/Users/<USER>/WebstormProjects/inCODE/ui/package.json", "plugins": {"./node_modules/@umijs/core/dist/service/servicePlugin": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "preset", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/core/dist/service/servicePlugin.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/core/dist/service/servicePlugin", "key": "servicePlugin"}, "@umijs/preset-umi": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "preset", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/index.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "@umijs/preset-umi", "key": "umi"}, "./node_modules/@umijs/preset-umi/dist/registerMethods": {"config": {}, "time": {"hooks": {"onStart": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/registerMethods.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/registerMethods", "key": "registerMethods"}, "@umijs/did-you-know": {"config": {}, "time": {"hooks": {"onStart": [1]}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/did-you-know/dist/plugin.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "@umijs/did-you-know", "key": "umijsDidYouKnow"}, "./node_modules/@umijs/preset-umi/dist/features/404/404": {"config": {}, "time": {"hooks": {"modifyRoutes": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/404/404.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/404/404", "key": "404"}, "./node_modules/@umijs/preset-umi/dist/features/appData/appData": {"config": {}, "time": {"hooks": {"modifyAppData": [127]}, "register": 8}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/appData/appData.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/appData/appData", "key": "appData"}, "./node_modules/@umijs/preset-umi/dist/features/appData/umiInfo": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/appData/umiInfo.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/appData/umiInfo", "key": "umiInfo"}, "./node_modules/@umijs/preset-umi/dist/features/check/check": {"config": {}, "time": {"hooks": {"onCheckConfig": [0], "onCheck": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/check/check.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/check/check", "key": "check"}, "./node_modules/@umijs/preset-umi/dist/features/check/babel722": {"config": {}, "time": {"hooks": {"onCheck": [1]}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/check/babel722.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/check/babel722", "key": "babel722"}, "./node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting", "key": "codeSplitting"}, "./node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins": {"config": {}, "time": {"hooks": {"modifyConfig": [0]}, "register": 4}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins", "key": "configPlugins"}, "virtual: config-title": {"id": "virtual: config-title", "key": "title", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-styles": {"id": "virtual: config-styles", "key": "styles", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-scripts": {"id": "virtual: config-scripts", "key": "scripts", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-routes": {"id": "virtual: config-routes", "key": "routes", "config": {"onChange": "regenerateTmpFiles"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-routeLoader": {"id": "virtual: config-routeLoader", "key": "routeLoader", "config": {"default": {"moduleType": "esm"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-reactRouter5Compat": {"id": "virtual: config-reactRouter5Compat", "key": "reactRouter5Compat", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-presets": {"id": "virtual: config-presets", "key": "presets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-plugins": {"id": "virtual: config-plugins", "key": "plugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-npmClient": {"id": "virtual: config-npmClient", "key": "npmClient", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mountElementId": {"id": "virtual: config-mountElementId", "key": "mountElementId", "config": {"default": "root"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-metas": {"id": "virtual: config-metas", "key": "metas", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-links": {"id": "virtual: config-links", "key": "links", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-historyWithQuery": {"id": "virtual: config-historyWithQuery", "key": "historyWithQuery", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-history": {"id": "virtual: config-history", "key": "history", "config": {"default": {"type": "browser"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-headScripts": {"id": "virtual: config-headScripts", "key": "headScripts", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-esbuildMinifyIIFE": {"id": "virtual: config-esbuildMinifyIIFE", "key": "esbuildMinifyIIFE", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-conventionRoutes": {"id": "virtual: config-conventionRoutes", "key": "conventionRoutes", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-conventionLayout": {"id": "virtual: config-conventionLayout", "key": "conventionLayout", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-base": {"id": "virtual: config-base", "key": "base", "config": {"default": "/"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-analyze": {"id": "virtual: config-analyze", "key": "analyze", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-writeToDisk": {"id": "virtual: config-writeToDisk", "key": "writeToDisk", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-transformRuntime": {"id": "virtual: config-transformRuntime", "key": "transformRuntime", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-theme": {"id": "virtual: config-theme", "key": "theme", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-targets": {"id": "virtual: config-targets", "key": "targets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-svgr": {"id": "virtual: config-svgr", "key": "svgr", "config": {"default": {}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-svgo": {"id": "virtual: config-svgo", "key": "svgo", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-stylusLoader": {"id": "virtual: config-stylusLoader", "key": "stylus<PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-styleLoader": {"id": "virtual: config-style<PERSON>oader", "key": "<PERSON><PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-srcTranspilerOptions": {"id": "virtual: config-srcTranspilerOptions", "key": "srcTranspilerOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-srcTranspiler": {"id": "virtual: config-srcTranspiler", "key": "srcTranspiler", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-sassLoader": {"id": "virtual: config-sassLoader", "key": "sass<PERSON><PERSON>der", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-runtimePublicPath": {"id": "virtual: config-runtimePublicPath", "key": "runtimePublicPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-purgeCSS": {"id": "virtual: config-purgeCSS", "key": "purgeCSS", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-publicPath": {"id": "virtual: config-publicPath", "key": "publicPath", "config": {"default": "/"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-proxy": {"id": "virtual: config-proxy", "key": "proxy", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-postcssLoader": {"id": "virtual: config-postcssLoader", "key": "postcss<PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-outputPath": {"id": "virtual: config-outputPath", "key": "outputPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-normalCSSLoaderModules": {"id": "virtual: config-normalCSSLoaderModules", "key": "normalCSSLoaderModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mfsu": {"id": "virtual: config-mfsu", "key": "mfsu", "config": {"default": {"strategy": "eager"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mdx": {"id": "virtual: config-mdx", "key": "mdx", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-manifest": {"id": "virtual: config-manifest", "key": "manifest", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-lessLoader": {"id": "virtual: config-less<PERSON><PERSON>der", "key": "<PERSON><PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-jsMinifierOptions": {"id": "virtual: config-jsMinifierOptions", "key": "jsMinifierOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-jsMinifier": {"id": "virtual: config-jsMinifier", "key": "jsMinifier", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-inlineLimit": {"id": "virtual: config-inlineLimit", "key": "inlineLimit", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-ignoreMomentLocale": {"id": "virtual: config-ignoreMomentLocale", "key": "ignoreMomentLocale", "config": {"default": true}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-https": {"id": "virtual: config-https", "key": "https", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-hash": {"id": "virtual: config-hash", "key": "hash", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-forkTSChecker": {"id": "virtual: config-fork<PERSON><PERSON><PERSON><PERSON>", "key": "forkTSChecker", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-fastRefresh": {"id": "virtual: config-fastRefresh", "key": "fastRefresh", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraPostCSSPlugins": {"id": "virtual: config-extraPostCSSPlugins", "key": "extraPostCSSPlugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelPresets": {"id": "virtual: config-extraBabelPresets", "key": "extraBabelPresets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelPlugins": {"id": "virtual: config-extraBabelPlugins", "key": "extraBabelPlugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelIncludes": {"id": "virtual: config-extraBabelIncludes", "key": "extraBabelIncludes", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-externals": {"id": "virtual: config-externals", "key": "externals", "config": {"default": {}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-esm": {"id": "virtual: config-esm", "key": "esm", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-devtool": {"id": "virtual: config-devtool", "key": "devtool", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-depTranspiler": {"id": "virtual: config-depTranspiler", "key": "depTranspiler", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-define": {"id": "virtual: config-define", "key": "define", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-deadCode": {"id": "virtual: config-deadCode", "key": "deadCode", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssPublicPath": {"id": "virtual: config-cssPublicPath", "key": "cssPublicPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssMinifierOptions": {"id": "virtual: config-cssMinifierOptions", "key": "cssMinifierOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssMinifier": {"id": "virtual: config-cssMinifier", "key": "cssMinifier", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssLoaderModules": {"id": "virtual: config-cssLoaderModules", "key": "cssLoaderModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssLoader": {"id": "virtual: config-cssLoader", "key": "cssL<PERSON>der", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-copy": {"id": "virtual: config-copy", "key": "copy", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-checkDepCssModules": {"id": "virtual: config-checkDepCssModules", "key": "checkDepCssModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-chainWebpack": {"id": "virtual: config-chainWebpack", "key": "chainWebpack", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cacheDirectoryPath": {"id": "virtual: config-cacheDirectoryPath", "key": "cacheDirectoryPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-babelLoaderCustomize": {"id": "virtual: config-babelLoaderCustomize", "key": "babelLoaderCustomize", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-autoprefixer": {"id": "virtual: config-autoprefixer", "key": "autoprefixer", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-autoCSSModules": {"id": "virtual: config-autoCSSModules", "key": "autoCSSModules", "config": {"default": true}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-alias": {"id": "virtual: config-alias", "key": "alias", "config": {"default": {"umi": "@@/exports", "react": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/react", "react-dom": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/react-dom", "react-router": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/node_modules/react-router", "react-router-dom": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/node_modules/react-router-dom"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "./node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin", "key": "crossorigin"}, "./node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand": {"config": {}, "time": {"hooks": {"onStart": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand", "key": "deps<PERSON>n<PERSON><PERSON><PERSON>"}, "./node_modules/@umijs/preset-umi/dist/features/devTool/devTool": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/devTool/devTool.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/devTool/devTool", "key": "devTool"}, "./node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker": {"config": {}, "time": {"hooks": {}, "register": 44}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker", "key": "esbuildHelperChecker"}, "./node_modules/@umijs/preset-umi/dist/features/esmi/esmi": {"config": {}, "time": {"hooks": {}, "register": 39}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/esmi/esmi.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/esmi/esmi", "key": "esmi"}, "./node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic": {"config": {}, "time": {"hooks": {}, "register": 8}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic", "key": "exportStatic"}, "./node_modules/@umijs/preset-umi/dist/features/favicons/favicons": {"config": {}, "time": {"hooks": {"modifyAppData": [0]}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/favicons/favicons.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/favicons/favicons", "key": "favicons"}, "./node_modules/@umijs/preset-umi/dist/features/helmet/helmet": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/helmet/helmet.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/helmet/helmet", "key": "helmet"}, "./node_modules/@umijs/preset-umi/dist/features/icons/icons": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/icons/icons.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/icons/icons", "key": "icons"}, "./node_modules/@umijs/preset-umi/dist/features/mock/mock": {"config": {}, "time": {"hooks": {"onStart": [0]}, "register": 16}, "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/mock/mock.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/mock/mock", "key": "mock"}, "./node_modules/@umijs/preset-umi/dist/features/mpa/mpa": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/mpa/mpa.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/mpa/mpa", "key": "mpa"}, "./node_modules/@umijs/preset-umi/dist/features/okam/okam": {"config": {}, "time": {"hooks": {}, "register": 0}, "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/okam/okam.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/okam/okam", "key": "okam"}, "./node_modules/@umijs/preset-umi/dist/features/overrides/overrides": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/overrides/overrides.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/overrides/overrides", "key": "overrides"}, "./node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency", "key": "phantomDependency"}, "./node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill": {"config": {}, "time": {"hooks": {"modifyConfig": [1]}, "register": 1}, "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill", "key": "polyfill"}, "./node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill", "key": "publicPathPolyfill"}, "./node_modules/@umijs/preset-umi/dist/features/prepare/prepare": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/prepare/prepare.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/prepare/prepare", "key": "prepare"}, "./node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch", "key": "routePrefetch"}, "./node_modules/@umijs/preset-umi/dist/features/terminal/terminal": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/terminal/terminal.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/terminal/terminal", "key": "terminal"}, "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles", "key": "tmpFiles"}, "./node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader", "key": "clientLoader"}, "./node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps": {"config": {}, "time": {"hooks": {}, "register": 0}, "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps", "key": "routeProps"}, "./node_modules/@umijs/preset-umi/dist/features/ssr/ssr": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/ssr/ssr.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/ssr/ssr", "key": "ssr"}, "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes", "key": "configTypes"}, "./node_modules/@umijs/preset-umi/dist/features/transform/transform": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/transform/transform.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/transform/transform", "key": "transform"}, "./node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport", "key": "lowImport"}, "./node_modules/@umijs/preset-umi/dist/features/vite/vite": {"config": {}, "time": {"hooks": {"modifyConfig": [0], "modifyDefaultConfig": [0], "modifyAppData": [0]}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/vite/vite.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/vite/vite", "key": "vite"}, "./node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute": {"config": {}, "time": {"hooks": {}, "register": 3}, "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute", "key": "apiRoute"}, "./node_modules/@umijs/preset-umi/dist/features/monorepo/redirect": {"config": {}, "time": {"hooks": {}, "register": 11}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/monorepo/redirect.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/monorepo/redirect", "key": "monorepoRedirect"}, "./node_modules/@umijs/preset-umi/dist/features/test/test": {"config": {}, "time": {"hooks": {}, "register": 0}, "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/test/test.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/test/test", "key": "test"}, "./node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent", "key": "clickToComponent"}, "./node_modules/@umijs/preset-umi/dist/features/legacy/legacy": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/legacy/legacy.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/legacy/legacy", "key": "legacy"}, "./node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose", "key": "classPropertiesLoose"}, "./node_modules/@umijs/preset-umi/dist/features/webpack/webpack": {"config": {}, "time": {"hooks": {}, "register": 0}, "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/webpack/webpack.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/webpack/webpack", "key": "preset-umi:webpack"}, "./node_modules/@umijs/preset-umi/dist/features/swc/swc": {"config": {}, "time": {"hooks": {"addOnDemandDeps": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/swc/swc.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/swc/swc", "key": "swc"}, "./node_modules/@umijs/preset-umi/dist/features/ui/ui": {"config": {}, "time": {"hooks": {}, "register": 1}, "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/ui/ui.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/ui/ui", "key": "ui"}, "./node_modules/@umijs/preset-umi/dist/features/mako/mako": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/mako/mako.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/mako/mako", "key": "mako"}, "./node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian": {"config": {}, "time": {"hooks": {}, "register": 1}, "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian", "key": "hm<PERSON><PERSON><PERSON><PERSON>"}, "./node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad": {"config": {}, "time": {"hooks": {}, "register": 0}, "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad", "key": "routePreloadOnLoad"}, "./node_modules/@umijs/preset-umi/dist/features/forget/forget": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/forget/forget.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/forget/forget", "key": "forget"}, "./node_modules/@umijs/preset-umi/dist/features/bundler/bundler": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/bundler/bundler.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/bundler/bundler", "key": "preset-umi:bundler"}, "./node_modules/@umijs/preset-umi/dist/commands/build": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/build.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/build", "key": "build"}, "./node_modules/@umijs/preset-umi/dist/commands/config/config": {"config": {}, "time": {"hooks": {}, "register": 18}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/config/config.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/config/config", "key": "config"}, "./node_modules/@umijs/preset-umi/dist/commands/dev/dev": {"config": {}, "time": {"hooks": {"modifyAppData": [7], "onStart": [0]}, "register": 13}, "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/dev/dev.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/dev/dev", "key": "dev"}, "./node_modules/@umijs/preset-umi/dist/commands/help": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/help.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/help", "key": "help"}, "./node_modules/@umijs/preset-umi/dist/commands/lint": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/lint.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/lint", "key": "lint"}, "./node_modules/@umijs/preset-umi/dist/commands/setup": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/setup.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/setup", "key": "setup"}, "./node_modules/@umijs/preset-umi/dist/commands/deadcode": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/deadcode.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/deadcode", "key": "deadcode"}, "./node_modules/@umijs/preset-umi/dist/commands/version": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/version.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/version", "key": "version"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/page": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/generators/page.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/page", "key": "generator:page"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/prettier": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/generators/prettier.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/prettier", "key": "generator:prettier"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig", "key": "generator:tsconfig"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/jest": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/generators/jest.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/jest", "key": "generator:jest"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss", "key": "generator:tailwindcss"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/dva": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/generators/dva.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/dva", "key": "generator:dva"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/component": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/generators/component.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/component", "key": "generator:component"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/mock": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/generators/mock.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/mock", "key": "generator:mock"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/cypress": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/generators/cypress.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/cypress", "key": "generator:cypress"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/api": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/generators/api.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/api", "key": "generator:api"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/precommit": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/generators/precommit.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/precommit", "key": "generator:precommit"}, "./node_modules/@umijs/preset-umi/dist/commands/plugin": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/plugin.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/plugin", "key": "command:plugin"}, "./node_modules/@umijs/preset-umi/dist/commands/verify-commit": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/verify-commit.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/verify-commit", "key": "verifyCommit"}, "./node_modules/@umijs/preset-umi/dist/commands/preview": {"config": {}, "time": {"hooks": {}, "register": 10}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/preview.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/preview", "key": "preview"}, "./node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu", "key": "mfsu-cli"}, "@umijs/plugin-run": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/plugin-run/dist/index.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "@umijs/plugin-run", "key": "run"}, "./node_modules/@umijs/core/dist/service/generatePlugin": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/core/dist/service/generatePlugin.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/core/dist/service/generatePlugin", "key": "generatePlugin"}, "./node_modules/@umijs/plugins/dist/antd": {"config": {}, "time": {"hooks": {"modifyConfig": [7], "modifyAppData": [0]}, "register": 3}, "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/plugins/dist/antd.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/plugins/dist/antd", "key": "antd"}, "./node_modules/@umijs/plugins/dist/model": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/plugins/dist/model.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/plugins/dist/model", "key": "model"}, "./node_modules/@umijs/plugins/dist/initial-state": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/plugins/dist/initial-state.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/plugins/dist/initial-state", "key": "initialState"}, "./node_modules/@umijs/plugins/dist/access": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/plugins/dist/access.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/plugins/dist/access", "key": "access"}}, "presets": [], "name": "dev", "args": {"_": []}, "userConfig": {"plugins": ["@umijs/plugins/dist/antd", "@umijs/plugins/dist/model", "@umijs/plugins/dist/initial-state", "@umijs/plugins/dist/access"], "vite": {}, "antd": {}, "model": {}, "initialState": {}, "access": {}, "npmClient": "npm", "alias": {"@": "/src"}}, "mainConfigFile": "/Users/<USER>/WebstormProjects/inCODE/ui/.umirc.ts", "config": {"routeLoader": {"moduleType": "esm"}, "mountElementId": "root", "history": {"type": "browser"}, "base": "/", "svgr": {}, "publicPath": "/", "mfsu": {"strategy": "eager"}, "ignoreMomentLocale": true, "externals": {}, "autoCSSModules": true, "alias": {"umi": "@@/exports", "react": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/react", "react-dom": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/react-dom", "react-router": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/node_modules/react-router", "react-router-dom": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/node_modules/react-router-dom", "@": "/Users/<USER>/WebstormProjects/inCODE/ui/src", "@@": "/Users/<USER>/WebstormProjects/inCODE/ui/src/.umi", "regenerator-runtime": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/regenerator-runtime", "@fs": "/Users/<USER>/WebstormProjects/inCODE/ui", "antd": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/antd"}, "polyfill": false, "plugins": ["@umijs/plugins/dist/antd", "@umijs/plugins/dist/model", "@umijs/plugins/dist/initial-state", "@umijs/plugins/dist/access"], "vite": {}, "antd": {}, "model": {}, "initialState": {}, "access": {}, "npmClient": "npm", "targets": {"chrome": 80}, "theme": {"blue-base": "#1890ff", "blue-1": "#e6f7ff", "blue-2": "#bae7ff", "blue-3": "#91d5ff", "blue-4": "#69c0ff", "blue-5": "#40a9ff", "blue-6": "#1890ff", "blue-7": "#096dd9", "blue-8": "#0050b3", "blue-9": "#003a8c", "blue-10": "#002766", "purple-base": "#722ed1", "purple-1": "#f9f0ff", "purple-2": "#efdbff", "purple-3": "#d3adf7", "purple-4": "#b37feb", "purple-5": "#9254de", "purple-6": "#722ed1", "purple-7": "#531dab", "purple-8": "#391085", "purple-9": "#22075e", "purple-10": "#120338", "cyan-base": "#13c2c2", "cyan-1": "#e6fffb", "cyan-2": "#b5f5ec", "cyan-3": "#87e8de", "cyan-4": "#5cdbd3", "cyan-5": "#36cfc9", "cyan-6": "#13c2c2", "cyan-7": "#08979c", "cyan-8": "#006d75", "cyan-9": "#00474f", "cyan-10": "#002329", "green-base": "#52c41a", "green-1": "#f6ffed", "green-2": "#d9f7be", "green-3": "#b7eb8f", "green-4": "#95de64", "green-5": "#73d13d", "green-6": "#52c41a", "green-7": "#389e0d", "green-8": "#237804", "green-9": "#135200", "green-10": "#092b00", "magenta-base": "#eb2f96", "magenta-1": "#fff0f6", "magenta-2": "#ffd6e7", "magenta-3": "#ffadd2", "magenta-4": "#ff85c0", "magenta-5": "#f759ab", "magenta-6": "#eb2f96", "magenta-7": "#c41d7f", "magenta-8": "#9e1068", "magenta-9": "#780650", "magenta-10": "#520339", "pink-base": "#eb2f96", "pink-1": "#fff0f6", "pink-2": "#ffd6e7", "pink-3": "#ffadd2", "pink-4": "#ff85c0", "pink-5": "#f759ab", "pink-6": "#eb2f96", "pink-7": "#c41d7f", "pink-8": "#9e1068", "pink-9": "#780650", "pink-10": "#520339", "red-base": "#f5222d", "red-1": "#fff1f0", "red-2": "#ffccc7", "red-3": "#ffa39e", "red-4": "#ff7875", "red-5": "#ff4d4f", "red-6": "#f5222d", "red-7": "#cf1322", "red-8": "#a8071a", "red-9": "#820014", "red-10": "#5c0011", "orange-base": "#fa8c16", "orange-1": "#fff7e6", "orange-2": "#ffe7ba", "orange-3": "#ffd591", "orange-4": "#ffc069", "orange-5": "#ffa940", "orange-6": "#fa8c16", "orange-7": "#d46b08", "orange-8": "#ad4e00", "orange-9": "#873800", "orange-10": "#612500", "yellow-base": "#fadb14", "yellow-1": "#feffe6", "yellow-2": "#ffffb8", "yellow-3": "#fffb8f", "yellow-4": "#fff566", "yellow-5": "#ffec3d", "yellow-6": "#fadb14", "yellow-7": "#d4b106", "yellow-8": "#ad8b00", "yellow-9": "#876800", "yellow-10": "#614700", "volcano-base": "#fa541c", "volcano-1": "#fff2e8", "volcano-2": "#ffd8bf", "volcano-3": "#ffbb96", "volcano-4": "#ff9c6e", "volcano-5": "#ff7a45", "volcano-6": "#fa541c", "volcano-7": "#d4380d", "volcano-8": "#ad2102", "volcano-9": "#871400", "volcano-10": "#610b00", "geekblue-base": "#2f54eb", "geekblue-1": "#f0f5ff", "geekblue-2": "#d6e4ff", "geekblue-3": "#adc6ff", "geekblue-4": "#85a5ff", "geekblue-5": "#597ef7", "geekblue-6": "#2f54eb", "geekblue-7": "#1d39c4", "geekblue-8": "#10239e", "geekblue-9": "#061178", "geekblue-10": "#030852", "lime-base": "#a0d911", "lime-1": "#fcffe6", "lime-2": "#f4ffb8", "lime-3": "#eaff8f", "lime-4": "#d3f261", "lime-5": "#bae637", "lime-6": "#a0d911", "lime-7": "#7cb305", "lime-8": "#5b8c00", "lime-9": "#3f6600", "lime-10": "#254000", "gold-base": "#faad14", "gold-1": "#fffbe6", "gold-2": "#fff1b8", "gold-3": "#ffe58f", "gold-4": "#ffd666", "gold-5": "#ffc53d", "gold-6": "#faad14", "gold-7": "#d48806", "gold-8": "#ad6800", "gold-9": "#874d00", "gold-10": "#613400", "preset-colors": "pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,", "theme": "default", "ant-prefix": "ant", "html-selector": "html", "primary-color": "#1890ff", "primary-color-hover": "#40a9ff", "primary-color-active": "#096dd9", "primary-color-outline": "rgba(24, 144, 255, 0.2)", "processing-color": "#1890ff", "info-color": "#1890ff", "info-color-deprecated-bg": "#e6f7ff", "info-color-deprecated-border": "#91d5ff", "success-color": "#52c41a", "success-color-hover": "#73d13d", "success-color-active": "#389e0d", "success-color-outline": "rgba(82, 196, 26, 0.2)", "success-color-deprecated-bg": "#f6ffed", "success-color-deprecated-border": "#b7eb8f", "warning-color": "#faad14", "warning-color-hover": "#ffc53d", "warning-color-active": "#d48806", "warning-color-outline": "rgba(250, 173, 20, 0.2)", "warning-color-deprecated-bg": "#fffbe6", "warning-color-deprecated-border": "#ffe58f", "error-color": "#ff4d4f", "error-color-hover": "#ff7875", "error-color-active": "#d9363e", "error-color-outline": "rgba(255, 77, 79, 0.2)", "error-color-deprecated-bg": "#fff2f0", "error-color-deprecated-border": "#ffccc7", "highlight-color": "#ff4d4f", "normal-color": "#d9d9d9", "white": "#fff", "black": "#000", "primary-1": "#e6f7ff", "primary-2": "#bae7ff", "primary-3": "#91d5ff", "primary-4": "#69c0ff", "primary-5": "#40a9ff", "primary-6": "#1890ff", "primary-7": "#096dd9", "primary-8": "#0050b3", "primary-9": "#003a8c", "primary-10": "#002766", "component-background": "#fff", "popover-background": "#fff", "popover-customize-border-color": "#f0f0f0", "font-family": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "code-family": "'SFMono-Regular', <PERSON><PERSON><PERSON>, 'Liberation Mono', Menlo, Courier, monospace", "text-color": "rgba(0, 0, 0, 0.85)", "text-color-secondary": "rgba(0, 0, 0, 0.45)", "text-color-inverse": "#fff", "icon-color": "inherit", "icon-color-hover": "rgba(0, 0, 0, 0.75)", "heading-color": "rgba(0, 0, 0, 0.85)", "text-color-dark": "rgba(255, 255, 255, 0.85)", "text-color-secondary-dark": "rgba(255, 255, 255, 0.65)", "text-selection-bg": "#1890ff", "font-variant-base": "tabular-nums", "font-feature-settings-base": "tnum", "font-size-base": "14px", "font-size-lg": "16px", "font-size-sm": "12px", "heading-1-size": "38px", "heading-2-size": "30px", "heading-3-size": "24px", "heading-4-size": "20px", "heading-5-size": "16px", "line-height-base": "1.5715", "border-radius-base": "2px", "border-radius-sm": "2px", "control-border-radius": "2px", "arrow-border-radius": "2px", "padding-lg": "24px", "padding-md": "16px", "padding-sm": "12px", "padding-xs": "8px", "padding-xss": "4px", "control-padding-horizontal": "12px", "control-padding-horizontal-sm": "8px", "margin-lg": "24px", "margin-md": "16px", "margin-sm": "12px", "margin-xs": "8px", "margin-xss": "4px", "height-base": "32px", "height-lg": "40px", "height-sm": "24px", "item-active-bg": "#e6f7ff", "item-hover-bg": "#f5f5f5", "iconfont-css-prefix": "anticon", "link-color": "#1890ff", "link-hover-color": "#40a9ff", "link-active-color": "#096dd9", "link-decoration": "none", "link-hover-decoration": "none", "link-focus-decoration": "none", "link-focus-outline": "0", "ease-base-out": "cubic-bezier(0.7, 0.3, 0.1, 1)", "ease-base-in": "cubic-bezier(0.9, 0, 0.3, 0.7)", "ease-out": "cubic-bezier(0.215, 0.61, 0.355, 1)", "ease-in": "cubic-bezier(0.55, 0.055, 0.675, 0.19)", "ease-in-out": "cubic-bezier(0.645, 0.045, 0.355, 1)", "ease-out-back": "cubic-bezier(0.12, 0.4, 0.29, 1.46)", "ease-in-back": "cubic-bezier(0.71, -0.46, 0.88, 0.6)", "ease-in-out-back": "cubic-bezier(0.71, -0.46, 0.29, 1.46)", "ease-out-circ": "cubic-bezier(0.08, 0.82, 0.17, 1)", "ease-in-circ": "cubic-bezier(0.6, 0.04, 0.98, 0.34)", "ease-in-out-circ": "cubic-bezier(0.78, 0.14, 0.15, 0.86)", "ease-out-quint": "cubic-bezier(0.23, 1, 0.32, 1)", "ease-in-quint": "cubic-bezier(0.755, 0.05, 0.855, 0.06)", "ease-in-out-quint": "cubic-bezier(0.86, 0, 0.07, 1)", "border-color-base": "#d9d9d9", "border-color-split": "#f0f0f0", "border-color-inverse": "#fff", "border-width-base": "1px", "border-style-base": "solid", "outline-blur-size": "0", "outline-width": "2px", "outline-color": "#1890ff", "outline-fade": "20%", "background-color-light": "#fafafa", "background-color-base": "#f5f5f5", "disabled-color": "rgba(0, 0, 0, 0.25)", "disabled-bg": "#f5f5f5", "disabled-active-bg": "#e6e6e6", "disabled-color-dark": "rgba(255, 255, 255, 0.35)", "shadow-color": "rgba(0, 0, 0, 0.15)", "shadow-color-inverse": "#fff", "box-shadow-base": "0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)", "shadow-1-up": "0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03)", "shadow-1-down": "0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03)", "shadow-1-left": "-6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03)", "shadow-1-right": "6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03)", "shadow-2": "0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)", "btn-font-weight": "400", "btn-border-radius-base": "2px", "btn-border-radius-sm": "2px", "btn-border-width": "1px", "btn-border-style": "solid", "btn-shadow": "0 2px 0 rgba(0, 0, 0, 0.015)", "btn-primary-shadow": "0 2px 0 rgba(0, 0, 0, 0.045)", "btn-text-shadow": "0 -1px 0 rgba(0, 0, 0, 0.12)", "btn-primary-color": "#fff", "btn-primary-bg": "#1890ff", "btn-default-color": "rgba(0, 0, 0, 0.85)", "btn-default-bg": "#fff", "btn-default-border": "#d9d9d9", "btn-danger-color": "#fff", "btn-danger-bg": "#ff4d4f", "btn-danger-border": "#ff4d4f", "btn-disable-color": "rgba(0, 0, 0, 0.25)", "btn-disable-bg": "#f5f5f5", "btn-disable-border": "#d9d9d9", "btn-default-ghost-color": "#fff", "btn-default-ghost-bg": "transparent", "btn-default-ghost-border": "#fff", "btn-font-size-lg": "16px", "btn-font-size-sm": "14px", "btn-padding-horizontal-base": "15px", "btn-padding-horizontal-lg": "15px", "btn-padding-horizontal-sm": "7px", "btn-height-base": "32px", "btn-height-lg": "40px", "btn-height-sm": "24px", "btn-line-height": "1.5715", "btn-circle-size": "32px", "btn-circle-size-lg": "40px", "btn-circle-size-sm": "24px", "btn-square-size": "32px", "btn-square-size-lg": "40px", "btn-square-size-sm": "24px", "btn-square-only-icon-size": "16px", "btn-square-only-icon-size-sm": "14px", "btn-square-only-icon-size-lg": "18px", "btn-group-border": "#40a9ff", "btn-link-hover-bg": "transparent", "btn-text-hover-bg": "rgba(0, 0, 0, 0.018)", "checkbox-size": "16px", "checkbox-color": "#1890ff", "checkbox-check-color": "#fff", "checkbox-check-bg": "#fff", "checkbox-border-width": "1px", "checkbox-border-radius": "2px", "checkbox-group-item-margin-right": "8px", "descriptions-bg": "#fafafa", "descriptions-title-margin-bottom": "20px", "descriptions-default-padding": "16px 24px", "descriptions-middle-padding": "12px 24px", "descriptions-small-padding": "8px 16px", "descriptions-item-padding-bottom": "16px", "descriptions-item-trailing-colon": "true", "descriptions-item-label-colon-margin-right": "8px", "descriptions-item-label-colon-margin-left": "2px", "descriptions-extra-color": "rgba(0, 0, 0, 0.85)", "divider-text-padding": "1em", "divider-orientation-margin": "5%", "divider-color": "rgba(0, 0, 0, 0.06)", "divider-vertical-gutter": "8px", "dropdown-selected-color": "#1890ff", "dropdown-menu-submenu-disabled-bg": "#fff", "dropdown-selected-bg": "#e6f7ff", "empty-font-size": "14px", "radio-size": "16px", "radio-top": "0.2em", "radio-border-width": "1px", "radio-dot-size": "8px", "radio-dot-color": "#1890ff", "radio-dot-disabled-color": "rgba(0, 0, 0, 0.2)", "radio-solid-checked-color": "#fff", "radio-button-bg": "#fff", "radio-button-checked-bg": "#fff", "radio-button-color": "rgba(0, 0, 0, 0.85)", "radio-button-hover-color": "#40a9ff", "radio-button-active-color": "#096dd9", "radio-button-padding-horizontal": "15px", "radio-disabled-button-checked-bg": "#e6e6e6", "radio-disabled-button-checked-color": "rgba(0, 0, 0, 0.25)", "radio-wrapper-margin-right": "8px", "screen-xs": "480px", "screen-xs-min": "480px", "screen-sm": "576px", "screen-sm-min": "576px", "screen-md": "768px", "screen-md-min": "768px", "screen-lg": "992px", "screen-lg-min": "992px", "screen-xl": "1200px", "screen-xl-min": "1200px", "screen-xxl": "1600px", "screen-xxl-min": "1600px", "screen-xs-max": "575px", "screen-sm-max": "767px", "screen-md-max": "991px", "screen-lg-max": "1199px", "screen-xl-max": "1599px", "grid-columns": "24", "layout-header-background": "#001529", "layout-header-height": "64px", "layout-header-padding": "0 50px", "layout-header-color": "rgba(0, 0, 0, 0.85)", "layout-footer-padding": "24px 50px", "layout-footer-background": "#f0f2f5", "layout-sider-background": "#001529", "layout-trigger-height": "48px", "layout-trigger-background": "#002140", "layout-trigger-color": "#fff", "layout-zero-trigger-width": "36px", "layout-zero-trigger-height": "42px", "layout-sider-background-light": "#fff", "layout-trigger-background-light": "#fff", "layout-trigger-color-light": "rgba(0, 0, 0, 0.85)", "zindex-badge": "auto", "zindex-table-fixed": "2", "zindex-affix": "10", "zindex-back-top": "10", "zindex-picker-panel": "10", "zindex-popup-close": "10", "zindex-modal": "1000", "zindex-modal-mask": "1000", "zindex-message": "1010", "zindex-notification": "1010", "zindex-popover": "1030", "zindex-dropdown": "1050", "zindex-picker": "1050", "zindex-popoconfirm": "1060", "zindex-tooltip": "1070", "zindex-image": "1080", "animation-duration-slow": "0.3s", "animation-duration-base": "0.2s", "animation-duration-fast": "0.1s", "collapse-panel-border-radius": "2px", "dropdown-menu-bg": "#fff", "dropdown-vertical-padding": "5px", "dropdown-edge-child-vertical-padding": "4px", "dropdown-font-size": "14px", "dropdown-line-height": "22px", "label-required-color": "#ff4d4f", "label-color": "rgba(0, 0, 0, 0.85)", "form-warning-input-bg": "#fff", "form-item-margin-bottom": "24px", "form-item-trailing-colon": "true", "form-vertical-label-padding": "0 0 8px", "form-vertical-label-margin": "0", "form-item-label-font-size": "14px", "form-item-label-height": "32px", "form-item-label-colon-margin-right": "8px", "form-item-label-colon-margin-left": "2px", "form-error-input-bg": "#fff", "input-height-base": "32px", "input-height-lg": "40px", "input-height-sm": "24px", "input-padding-horizontal": "11px", "input-padding-horizontal-base": "11px", "input-padding-horizontal-sm": "7px", "input-padding-horizontal-lg": "11px", "input-padding-vertical-base": "4px", "input-padding-vertical-sm": "0px", "input-padding-vertical-lg": "6.5px", "input-placeholder-color": "#bfbfbf", "input-color": "rgba(0, 0, 0, 0.85)", "input-icon-color": "rgba(0, 0, 0, 0.85)", "input-border-color": "#d9d9d9", "input-bg": "#fff", "input-number-hover-border-color": "#40a9ff", "input-number-handler-active-bg": "#f4f4f4", "input-number-handler-hover-bg": "#40a9ff", "input-number-handler-bg": "#fff", "input-number-handler-border-color": "#d9d9d9", "input-addon-bg": "#fafafa", "input-hover-border-color": "#40a9ff", "input-disabled-bg": "#f5f5f5", "input-outline-offset": "0 0", "input-icon-hover-color": "rgba(0, 0, 0, 0.85)", "input-disabled-color": "rgba(0, 0, 0, 0.25)", "mentions-dropdown-bg": "#fff", "mentions-dropdown-menu-item-hover-bg": "#fff", "select-border-color": "#d9d9d9", "select-item-selected-color": "rgba(0, 0, 0, 0.85)", "select-item-selected-font-weight": "600", "select-dropdown-bg": "#fff", "select-item-selected-bg": "#e6f7ff", "select-item-active-bg": "#f5f5f5", "select-dropdown-vertical-padding": "5px", "select-dropdown-font-size": "14px", "select-dropdown-line-height": "22px", "select-dropdown-height": "32px", "select-background": "#fff", "select-clear-background": "#fff", "select-selection-item-bg": "#f5f5f5", "select-selection-item-border-color": "#f0f0f0", "select-single-item-height-lg": "40px", "select-multiple-item-height": "24px", "select-multiple-item-height-lg": "32px", "select-multiple-item-spacing-half": "2px", "select-multiple-disabled-background": "#f5f5f5", "select-multiple-item-disabled-color": "#bfbfbf", "select-multiple-item-disabled-border-color": "#d9d9d9", "cascader-bg": "#fff", "cascader-item-selected-bg": "#e6f7ff", "cascader-menu-bg": "#fff", "cascader-menu-border-color-split": "#f0f0f0", "cascader-dropdown-vertical-padding": "5px", "cascader-dropdown-edge-child-vertical-padding": "4px", "cascader-dropdown-font-size": "14px", "cascader-dropdown-line-height": "22px", "anchor-bg": "transparent", "anchor-border-color": "#f0f0f0", "anchor-link-top": "4px", "anchor-link-left": "16px", "anchor-link-padding": "4px 0 4px 16px", "tooltip-max-width": "250px", "tooltip-color": "#fff", "tooltip-bg": "rgba(0, 0, 0, 0.75)", "tooltip-arrow-width": "11.3137085px", "tooltip-distance": "14.3137085px", "tooltip-arrow-color": "rgba(0, 0, 0, 0.75)", "tooltip-border-radius": "2px", "popover-bg": "#fff", "popover-color": "rgba(0, 0, 0, 0.85)", "popover-min-width": "177px", "popover-min-height": "32px", "popover-arrow-width": "11.3137085px", "popover-arrow-color": "#fff", "popover-arrow-outer-color": "#fff", "popover-distance": "15.3137085px", "popover-padding-horizontal": "16px", "modal-header-padding-vertical": "16px", "modal-header-padding-horizontal": "24px", "modal-header-bg": "#fff", "modal-header-padding": "16px 24px", "modal-header-border-width": "1px", "modal-header-border-style": "solid", "modal-header-title-line-height": "22px", "modal-header-title-font-size": "16px", "modal-header-border-color-split": "#f0f0f0", "modal-header-close-size": "54px", "modal-content-bg": "#fff", "modal-heading-color": "rgba(0, 0, 0, 0.85)", "modal-close-color": "rgba(0, 0, 0, 0.45)", "modal-footer-bg": "transparent", "modal-footer-border-color-split": "#f0f0f0", "modal-footer-border-style": "solid", "modal-footer-padding-vertical": "10px", "modal-footer-padding-horizontal": "16px", "modal-footer-border-width": "1px", "modal-mask-bg": "rgba(0, 0, 0, 0.45)", "modal-confirm-title-font-size": "16px", "modal-border-radius": "2px", "progress-default-color": "#1890ff", "progress-remaining-color": "#f5f5f5", "progress-info-text-color": "rgba(0, 0, 0, 0.85)", "progress-radius": "100px", "progress-steps-item-bg": "#f3f3f3", "progress-text-font-size": "1em", "progress-text-color": "rgba(0, 0, 0, 0.85)", "progress-circle-text-font-size": "1em", "menu-inline-toplevel-item-height": "40px", "menu-item-height": "40px", "menu-item-group-height": "1.5715", "menu-collapsed-width": "80px", "menu-bg": "#fff", "menu-popup-bg": "#fff", "menu-item-color": "rgba(0, 0, 0, 0.85)", "menu-inline-submenu-bg": "#fafafa", "menu-highlight-color": "#1890ff", "menu-highlight-danger-color": "#ff4d4f", "menu-item-active-bg": "#e6f7ff", "menu-item-active-danger-bg": "#fff1f0", "menu-item-active-border-width": "3px", "menu-item-group-title-color": "rgba(0, 0, 0, 0.45)", "menu-item-vertical-margin": "4px", "menu-item-font-size": "14px", "menu-item-boundary-margin": "8px", "menu-item-padding-horizontal": "20px", "menu-item-padding": "0 20px", "menu-horizontal-line-height": "46px", "menu-icon-margin-right": "10px", "menu-icon-size": "14px", "menu-icon-size-lg": "16px", "menu-item-group-title-font-size": "14px", "menu-dark-color": "rgba(255, 255, 255, 0.65)", "menu-dark-danger-color": "#ff4d4f", "menu-dark-bg": "#001529", "menu-dark-arrow-color": "#fff", "menu-dark-inline-submenu-bg": "#000c17", "menu-dark-highlight-color": "#fff", "menu-dark-item-active-bg": "#1890ff", "menu-dark-item-active-danger-bg": "#ff4d4f", "menu-dark-selected-item-icon-color": "#fff", "menu-dark-selected-item-text-color": "#fff", "menu-dark-item-hover-bg": "transparent", "spin-dot-size-sm": "14px", "spin-dot-size": "20px", "spin-dot-size-lg": "32px", "table-bg": "#fff", "table-header-bg": "#fafafa", "table-header-color": "rgba(0, 0, 0, 0.85)", "table-header-sort-bg": "#f5f5f5", "table-row-hover-bg": "#fafafa", "table-selected-row-color": "inherit", "table-selected-row-bg": "#e6f7ff", "table-selected-row-hover-bg": "#dcf4ff", "table-expanded-row-bg": "#fbfbfb", "table-padding-vertical": "16px", "table-padding-horizontal": "16px", "table-padding-vertical-md": "12px", "table-padding-horizontal-md": "8px", "table-padding-vertical-sm": "8px", "table-padding-horizontal-sm": "8px", "table-border-color": "#f0f0f0", "table-border-radius-base": "2px", "table-footer-bg": "#fafafa", "table-footer-color": "rgba(0, 0, 0, 0.85)", "table-header-bg-sm": "#fafafa", "table-font-size": "14px", "table-font-size-md": "14px", "table-font-size-sm": "14px", "table-header-cell-split-color": "rgba(0, 0, 0, 0.06)", "table-header-sort-active-bg": "rgba(0, 0, 0, 0.04)", "table-fixed-header-sort-active-bg": "#f5f5f5", "table-header-filter-active-bg": "rgba(0, 0, 0, 0.04)", "table-filter-btns-bg": "inherit", "table-filter-dropdown-bg": "#fff", "table-expand-icon-bg": "#fff", "table-selection-column-width": "32px", "table-sticky-scroll-bar-bg": "rgba(0, 0, 0, 0.35)", "table-sticky-scroll-bar-radius": "4px", "tag-border-radius": "2px", "tag-default-bg": "#fafafa", "tag-default-color": "rgba(0, 0, 0, 0.85)", "tag-font-size": "12px", "tag-line-height": "20px", "picker-bg": "#fff", "picker-basic-cell-hover-color": "#f5f5f5", "picker-basic-cell-active-with-range-color": "#e6f7ff", "picker-basic-cell-hover-with-range-color": "#cbe6ff", "picker-basic-cell-disabled-bg": "rgba(0, 0, 0, 0.04)", "picker-border-color": "#f0f0f0", "picker-date-hover-range-border-color": "#7ec1ff", "picker-date-hover-range-color": "#cbe6ff", "picker-time-panel-column-width": "56px", "picker-time-panel-column-height": "224px", "picker-time-panel-cell-height": "28px", "picker-panel-cell-height": "24px", "picker-panel-cell-width": "36px", "picker-text-height": "40px", "picker-panel-without-time-cell-height": "66px", "calendar-bg": "#fff", "calendar-input-bg": "#fff", "calendar-border-color": "#fff", "calendar-item-active-bg": "#e6f7ff", "calendar-column-active-bg": "rgba(230, 247, 255, 0.2)", "calendar-full-bg": "#fff", "calendar-full-panel-bg": "#fff", "carousel-dot-width": "16px", "carousel-dot-height": "3px", "carousel-dot-active-width": "24px", "badge-height": "20px", "badge-height-sm": "14px", "badge-dot-size": "6px", "badge-font-size": "12px", "badge-font-size-sm": "12px", "badge-font-weight": "normal", "badge-status-size": "6px", "badge-text-color": "#fff", "badge-color": "#ff4d4f", "rate-star-color": "#fadb14", "rate-star-bg": "#f0f0f0", "rate-star-size": "20px", "rate-star-hover-scale": "scale(1.1)", "card-head-color": "rgba(0, 0, 0, 0.85)", "card-head-background": "transparent", "card-head-font-size": "16px", "card-head-font-size-sm": "14px", "card-head-padding": "16px", "card-head-padding-sm": "8px", "card-head-height": "48px", "card-head-height-sm": "36px", "card-inner-head-padding": "12px", "card-padding-base": "24px", "card-padding-base-sm": "12px", "card-actions-background": "#fff", "card-actions-li-margin": "12px 0", "card-skeleton-bg": "#cfd8dc", "card-background": "#fff", "card-shadow": "0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09)", "card-radius": "2px", "card-head-tabs-margin-bottom": "-17px", "card-head-extra-color": "rgba(0, 0, 0, 0.85)", "comment-bg": "inherit", "comment-padding-base": "16px 0", "comment-nest-indent": "44px", "comment-font-size-base": "14px", "comment-font-size-sm": "12px", "comment-author-name-color": "rgba(0, 0, 0, 0.45)", "comment-author-time-color": "#ccc", "comment-action-color": "rgba(0, 0, 0, 0.45)", "comment-action-hover-color": "#595959", "comment-actions-margin-bottom": "inherit", "comment-actions-margin-top": "12px", "comment-content-detail-p-margin-bottom": "inherit", "tabs-card-head-background": "#fafafa", "tabs-card-height": "40px", "tabs-card-active-color": "#1890ff", "tabs-card-horizontal-padding": "8px 16px", "tabs-card-horizontal-padding-sm": "6px 16px", "tabs-card-horizontal-padding-lg": "7px 16px 6px", "tabs-title-font-size": "14px", "tabs-title-font-size-lg": "16px", "tabs-title-font-size-sm": "14px", "tabs-ink-bar-color": "#1890ff", "tabs-bar-margin": "0 0 16px 0", "tabs-horizontal-gutter": "32px", "tabs-horizontal-margin": "0 0 0 32px", "tabs-horizontal-margin-rtl": "0 0 0 32px", "tabs-horizontal-padding": "12px 0", "tabs-horizontal-padding-lg": "16px 0", "tabs-horizontal-padding-sm": "8px 0", "tabs-vertical-padding": "8px 24px", "tabs-vertical-margin": "16px 0 0 0", "tabs-scrolling-size": "32px", "tabs-highlight-color": "#1890ff", "tabs-hover-color": "#40a9ff", "tabs-active-color": "#096dd9", "tabs-card-gutter": "2px", "tabs-card-tab-active-border-top": "2px solid transparent", "back-top-color": "#fff", "back-top-bg": "rgba(0, 0, 0, 0.45)", "back-top-hover-bg": "rgba(0, 0, 0, 0.85)", "avatar-size-base": "32px", "avatar-size-lg": "40px", "avatar-size-sm": "24px", "avatar-font-size-base": "18px", "avatar-font-size-lg": "24px", "avatar-font-size-sm": "14px", "avatar-bg": "#ccc", "avatar-color": "#fff", "avatar-border-radius": "2px", "avatar-group-overlapping": "-8px", "avatar-group-space": "3px", "avatar-group-border-color": "#fff", "switch-height": "22px", "switch-sm-height": "16px", "switch-min-width": "44px", "switch-sm-min-width": "28px", "switch-disabled-opacity": "0.4", "switch-color": "#1890ff", "switch-bg": "#fff", "switch-shadow-color": "rgba(0, 35, 11, 0.2)", "switch-padding": "2px", "switch-inner-margin-min": "7px", "switch-inner-margin-max": "25px", "switch-sm-inner-margin-min": "5px", "switch-sm-inner-margin-max": "18px", "pagination-item-bg": "#fff", "pagination-item-size": "32px", "pagination-item-size-sm": "24px", "pagination-font-family": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "pagination-font-weight-active": "500", "pagination-item-bg-active": "#fff", "pagination-item-link-bg": "#fff", "pagination-item-disabled-color-active": "rgba(0, 0, 0, 0.25)", "pagination-item-disabled-bg-active": "#e6e6e6", "pagination-item-input-bg": "#fff", "pagination-mini-options-size-changer-top": "0px", "page-header-padding": "24px", "page-header-padding-vertical": "16px", "page-header-padding-breadcrumb": "12px", "page-header-content-padding-vertical": "12px", "page-header-back-color": "#000", "page-header-ghost-bg": "inherit", "page-header-heading-title": "20px", "page-header-heading-sub-title": "14px", "page-header-tabs-tab-font-size": "16px", "breadcrumb-base-color": "rgba(0, 0, 0, 0.45)", "breadcrumb-last-item-color": "rgba(0, 0, 0, 0.85)", "breadcrumb-font-size": "14px", "breadcrumb-icon-font-size": "14px", "breadcrumb-link-color": "rgba(0, 0, 0, 0.45)", "breadcrumb-link-color-hover": "rgba(0, 0, 0, 0.85)", "breadcrumb-separator-color": "rgba(0, 0, 0, 0.45)", "breadcrumb-separator-margin": "0 8px", "slider-margin": "10px 6px 10px", "slider-rail-background-color": "#f5f5f5", "slider-rail-background-color-hover": "#e1e1e1", "slider-track-background-color": "#91d5ff", "slider-track-background-color-hover": "#69c0ff", "slider-handle-border-width": "2px", "slider-handle-background-color": "#fff", "slider-handle-color": "#91d5ff", "slider-handle-color-hover": "#69c0ff", "slider-handle-color-focus": "#46a6ff", "slider-handle-color-focus-shadow": "rgba(24, 144, 255, 0.12)", "slider-handle-color-tooltip-open": "#1890ff", "slider-handle-size": "14px", "slider-handle-margin-top": "-5px", "slider-handle-shadow": "0", "slider-dot-border-color": "#f0f0f0", "slider-dot-border-color-active": "#8cc8ff", "slider-disabled-color": "rgba(0, 0, 0, 0.25)", "slider-disabled-background-color": "#fff", "tree-bg": "#fff", "tree-title-height": "24px", "tree-child-padding": "18px", "tree-directory-selected-color": "#fff", "tree-directory-selected-bg": "#1890ff", "tree-node-hover-bg": "#f5f5f5", "tree-node-selected-bg": "#bae7ff", "collapse-header-padding": "12px 16px", "collapse-header-padding-extra": "40px", "collapse-header-bg": "#fafafa", "collapse-content-padding": "16px", "collapse-content-bg": "#fff", "collapse-header-arrow-left": "16px", "skeleton-color": "rgba(190, 190, 190, 0.2)", "skeleton-to-color": "rgba(129, 129, 129, 0.24)", "skeleton-paragraph-margin-top": "28px", "skeleton-paragraph-li-margin-top": "16px", "skeleton-paragraph-li-height": "16px", "skeleton-title-height": "16px", "skeleton-title-paragraph-margin-top": "24px", "transfer-header-height": "40px", "transfer-item-height": "32px", "transfer-disabled-bg": "#f5f5f5", "transfer-list-height": "200px", "transfer-item-hover-bg": "#f5f5f5", "transfer-item-selected-hover-bg": "#dcf4ff", "transfer-item-padding-vertical": "6px", "transfer-list-search-icon-top": "12px", "message-notice-content-padding": "10px 16px", "message-notice-content-bg": "#fff", "wave-animation-width": "6px", "alert-success-border-color": "#b7eb8f", "alert-success-bg-color": "#f6ffed", "alert-success-icon-color": "#52c41a", "alert-info-border-color": "#91d5ff", "alert-info-bg-color": "#e6f7ff", "alert-info-icon-color": "#1890ff", "alert-warning-border-color": "#ffe58f", "alert-warning-bg-color": "#fffbe6", "alert-warning-icon-color": "#faad14", "alert-error-border-color": "#ffccc7", "alert-error-bg-color": "#fff2f0", "alert-error-icon-color": "#ff4d4f", "alert-message-color": "rgba(0, 0, 0, 0.85)", "alert-text-color": "rgba(0, 0, 0, 0.85)", "alert-close-color": "rgba(0, 0, 0, 0.45)", "alert-close-hover-color": "rgba(0, 0, 0, 0.75)", "alert-no-icon-padding-vertical": "8px", "alert-with-description-no-icon-padding-vertical": "15px", "alert-with-description-padding-vertical": "15px", "alert-with-description-padding": "15px 15px 15px 24px", "alert-icon-top": "12.0005px", "alert-with-description-icon-size": "24px", "list-header-background": "transparent", "list-footer-background": "transparent", "list-empty-text-padding": "16px", "list-item-padding": "12px 0", "list-item-padding-sm": "8px 16px", "list-item-padding-lg": "16px 24px", "list-item-meta-margin-bottom": "16px", "list-item-meta-avatar-margin-right": "16px", "list-item-meta-title-margin-bottom": "12px", "list-customize-card-bg": "#fff", "list-item-meta-description-font-size": "14px", "statistic-title-font-size": "14px", "statistic-content-font-size": "24px", "statistic-unit-font-size": "24px", "statistic-font-family": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "drawer-header-padding": "16px 24px", "drawer-bg": "#fff", "drawer-footer-padding-vertical": "10px", "drawer-footer-padding-horizontal": "16px", "drawer-header-close-size": "56px", "drawer-title-font-size": "16px", "drawer-title-line-height": "22px", "timeline-width": "2px", "timeline-color": "#f0f0f0", "timeline-dot-border-width": "2px", "timeline-dot-color": "#1890ff", "timeline-dot-bg": "#fff", "timeline-item-padding-bottom": "20px", "typography-title-font-weight": "600", "typography-title-margin-top": "1.2em", "typography-title-margin-bottom": "0.5em", "upload-actions-color": "rgba(0, 0, 0, 0.45)", "process-tail-color": "#f0f0f0", "steps-nav-arrow-color": "rgba(0, 0, 0, 0.25)", "steps-background": "#fff", "steps-icon-size": "32px", "steps-icon-custom-size": "32px", "steps-icon-custom-top": "0px", "steps-icon-custom-font-size": "24px", "steps-icon-top": "-0.5px", "steps-icon-font-size": "16px", "steps-icon-margin": "0 8px 0 0", "steps-title-line-height": "32px", "steps-small-icon-size": "24px", "steps-small-icon-margin": "0 8px 0 0", "steps-dot-size": "8px", "steps-dot-top": "2px", "steps-current-dot-size": "10px", "steps-description-max-width": "140px", "steps-nav-content-max-width": "auto", "steps-vertical-icon-width": "16px", "steps-vertical-tail-width": "16px", "steps-vertical-tail-width-sm": "12px", "notification-bg": "#fff", "notification-padding-vertical": "16px", "notification-padding-horizontal": "24px", "result-title-font-size": "24px", "result-subtitle-font-size": "14px", "result-icon-font-size": "72px", "result-extra-margin": "24px 0 0 0", "image-size-base": "48px", "image-font-size-base": "24px", "image-bg": "#f5f5f5", "image-color": "#fff", "image-mask-font-size": "16px", "image-preview-operation-size": "18px", "image-preview-operation-color": "rgba(255, 255, 255, 0.85)", "image-preview-operation-disabled-color": "rgba(255, 255, 255, 0.25)", "segmented-bg": "rgba(0, 0, 0, 0.04)", "segmented-hover-bg": "rgba(0, 0, 0, 0.06)", "segmented-selected-bg": "#fff", "segmented-label-color": "rgba(0, 0, 0, 0.65)", "segmented-label-hover-color": "#262626"}}, "routes": {"admin/Dashboard": {"path": "admin/Dashboard", "id": "admin/Dashboard", "parentId": "@@/global-layout", "file": "admin/Dashboard.tsx", "absPath": "/admin/Dashboard", "__content": "import React, { useState, useEffect } from 'react';\nimport { Layout, Menu, Card, Row, Col, Statistic, Table, Tag, Button, Avatar, Dropdown, Space } from 'antd';\nimport {\n  DashboardOutlined,\n  ToolOutlined,\n  FileTextOutlined,\n  BookOutlined,\n  UserOutlined,\n  LogoutOutlined,\n  SettingOutlined,\n  PlusOutlined,\n  EyeOutlined,\n  EditOutlined,\n  DeleteOutlined\n} from '@ant-design/icons';\nimport { history } from 'umi';\n\nconst { Header, Sider, Content } = Layout;\n\nconst AdminDashboard: React.FC = () => {\n  const [collapsed, setCollapsed] = useState(false);\n  const [currentMenu, setCurrentMenu] = useState('dashboard');\n  const [user, setUser] = useState<any>(null);\n  const [stats, setStats] = useState({\n    toolCount: 0,\n    changelogCount: 0,\n    docCount: 0,\n    viewCount: 0\n  });\n  const navigate = history.push;\n\n  useEffect(() => {\n    // 获取用户信息\n    const userInfo = localStorage.getItem('admin_user');\n    if (userInfo) {\n      setUser(JSON.parse(userInfo));\n    } else {\n      navigate('/admin/login');\n    }\n\n    // 获取统计数据\n    fetchStats();\n  }, []);\n\n  const fetchStats = async () => {\n    try {\n      // 这里应该调用实际的统计API\n      setStats({\n        toolCount: 12,\n        changelogCount: 45,\n        docCount: 28,\n        viewCount: 15420\n      });\n    } catch (error) {\n      console.error('获取统计数据失败:', error);\n    }\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem('admin_token');\n    localStorage.removeItem('admin_user');\n    navigate('/admin/login');\n  };\n\n  const userMenu = (\n    <Menu>\n      <Menu.Item key=\"profile\" icon={<UserOutlined />}>\n        个人资料\n      </Menu.Item>\n      <Menu.Item key=\"settings\" icon={<SettingOutlined />}>\n        设置\n      </Menu.Item>\n      <Menu.Divider />\n      <Menu.Item key=\"logout\" icon={<LogoutOutlined />} onClick={handleLogout}>\n        退出登录\n      </Menu.Item>\n    </Menu>\n  );\n\n  const sidebarMenu = [\n    {\n      key: 'dashboard',\n      icon: <DashboardOutlined />,\n      label: '仪表板'\n    },\n    {\n      key: 'tools',\n      icon: <ToolOutlined />,\n      label: '工具管理'\n    },\n    {\n      key: 'changelogs',\n      icon: <FileTextOutlined />,\n      label: '更新日志'\n    },\n    {\n      key: 'help-docs',\n      icon: <BookOutlined />,\n      label: '帮助文档'\n    }\n  ];\n\n  const renderDashboardContent = () => (\n    <div>\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: '24px' }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"工具总数\"\n              value={stats.toolCount}\n              prefix={<ToolOutlined />}\n              valueStyle={{ color: '#3f8600' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"更新日志\"\n              value={stats.changelogCount}\n              prefix={<FileTextOutlined />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"帮助文档\"\n              value={stats.docCount}\n              prefix={<BookOutlined />}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"总浏览量\"\n              value={stats.viewCount}\n              prefix={<EyeOutlined />}\n              valueStyle={{ color: '#cf1322' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 最近活动 */}\n      <Row gutter={16}>\n        <Col span={12}>\n          <Card title=\"最新工具\" extra={<Button type=\"link\">查看全部</Button>}>\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>\n              {[\n                { name: 'Code Analyzer', version: '2.1.0', status: 'active' },\n                { name: 'API Tester', version: '1.5.2', status: 'active' },\n                { name: 'Deploy Helper', version: '3.0.1', status: 'active' }\n              ].map((tool, index) => (\n                <div key={index} style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                  <div>\n                    <div style={{ fontWeight: 'bold' }}>{tool.name}</div>\n                    <div style={{ color: '#666', fontSize: '12px' }}>版本 {tool.version}</div>\n                  </div>\n                  <Tag color=\"green\">活跃</Tag>\n                </div>\n              ))}\n            </div>\n          </Card>\n        </Col>\n        <Col span={12}>\n          <Card title=\"最新更新日志\" extra={<Button type=\"link\">查看全部</Button>}>\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>\n              {[\n                { title: '新增代码质量评分功能', version: '2.1.0', date: '2024-01-15' },\n                { title: '修复内存泄漏问题', version: '2.0.5', date: '2024-01-10' },\n                { title: '支持GraphQL协议测试', version: '1.5.2', date: '2024-01-08' }\n              ].map((log, index) => (\n                <div key={index}>\n                  <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>{log.title}</div>\n                  <div style={{ color: '#666', fontSize: '12px' }}>\n                    版本 {log.version} • {log.date}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </Card>\n        </Col>\n      </Row>\n    </div>\n  );\n\n  const renderContent = () => {\n    switch (currentMenu) {\n      case 'dashboard':\n        return renderDashboardContent();\n      case 'tools':\n        return <div>工具管理页面（待实现）</div>;\n      case 'changelogs':\n        return <div>更新日志管理页面（待实现）</div>;\n      case 'help-docs':\n        return <div>帮助文档管理页面（待实现）</div>;\n      default:\n        return renderDashboardContent();\n    }\n  };\n\n  return (\n    <Layout style={{ minHeight: '100vh' }}>\n      <Sider trigger={null} collapsible collapsed={collapsed}>\n        <div style={{\n          height: '64px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: 'white',\n          fontSize: '18px',\n          fontWeight: 'bold'\n        }}>\n          {collapsed ? 'iC' : 'inCODE'}\n        </div>\n        <Menu\n          theme=\"dark\"\n          mode=\"inline\"\n          selectedKeys={[currentMenu]}\n          items={sidebarMenu}\n          onClick={({ key }) => setCurrentMenu(key)}\n        />\n      </Sider>\n      \n      <Layout>\n        <Header style={{\n          padding: '0 24px',\n          background: '#fff',\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          boxShadow: '0 1px 4px rgba(0,21,41,.08)'\n        }}>\n          <Button\n            type=\"text\"\n            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n            onClick={() => setCollapsed(!collapsed)}\n            style={{ fontSize: '16px', width: 64, height: 64 }}\n          />\n          \n          <Space>\n            <span>欢迎回来，{user?.realName || user?.username}</span>\n            <Dropdown overlay={userMenu} placement=\"bottomRight\">\n              <Avatar\n                style={{ backgroundColor: '#1890ff', cursor: 'pointer' }}\n                icon={<UserOutlined />}\n              />\n            </Dropdown>\n          </Space>\n        </Header>\n        \n        <Content style={{\n          margin: '24px',\n          padding: '24px',\n          background: '#fff',\n          borderRadius: '6px'\n        }}>\n          {renderContent()}\n        </Content>\n      </Layout>\n    </Layout>\n  );\n};\n\nexport default AdminDashboard;\n", "__isJSFile": true, "__absFile": "/Users/<USER>/WebstormProjects/inCODE/ui/src/pages/admin/Dashboard.tsx"}, "display/about": {"path": "display/about", "id": "display/about", "parentId": "@@/global-layout", "file": "display/about.tsx", "absPath": "/display/about", "__content": "import React from 'react';\nimport { Typography, Card } from 'antd';\n\nconst { Title, Paragraph } = Typography;\n\nconst AboutPage: React.FC = () => {\n  return (\n    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>\n      <Card>\n        <Title level={2}>关于 inCODE 工具链平台</Title>\n        <Paragraph>\n          inCODE 工具链平台致力于为开发者提供一站式的工具发现、管理和使用体验。\n          我们汇集了各类开发工具，包括代码分析、测试、部署、文档生成等，旨在提升开发效率和项目质量。\n        </Paragraph>\n        <Paragraph>\n          我们的愿景是构建一个开放、共享的开发者社区，让优秀的工具能够被更多人发现和利用。\n        </Paragraph>\n        <Title level={3}>联系我们</Title>\n        <Paragraph>\n          如果您有任何疑问、建议或合作意向，请随时通过以下方式联系我们：\n        </Paragraph>\n        <ul>\n          <li>邮箱：<a href=\"mailto:<EMAIL>\"><EMAIL></a></li>\n          <li>GitHub：<a href=\"https://github.com/inCODE\" target=\"_blank\" rel=\"noopener noreferrer\">github.com/inCODE</a></li>\n        </ul>\n      </Card>\n    </div>\n  );\n};\n\nexport default AboutPage;\n", "__isJSFile": true, "__absFile": "/Users/<USER>/WebstormProjects/inCODE/ui/src/pages/display/about.tsx"}, "display/index": {"path": "display", "id": "display/index", "parentId": "@@/global-layout", "file": "display/index.tsx", "absPath": "/display", "__content": "import React, { useState } from 'react';\nimport { Row, Col, Typography, Space, Input, Select, Pagination, Spin } from 'antd';\nimport { useRequest } from 'ahooks';\nimport ToolCard from '@/components/ToolCard'; // 导入 ToolCard 组件\n\nconst { Title, Paragraph } = Typography;\nconst { Search } = Input;\nconst { Option } = Select;\n\ninterface Tool {\n  id: number;\n  name: string;\n  title: string;\n  description: string;\n  logoUrl?: string;\n  version: string;\n  category: string;\n  tags: string[];\n  githubUrl?: string;\n  downloadUrl?: string;\n  documentationUrl?: string;\n  viewCount: number;\n  updatedAt: string;\n}\n\n// 封装服务\nasync function fetchToolsService(params: { category?: string; keyword?: string; current: number; pageSize: number }) {\n  const url = new URL('http://localhost:8080/api/public/tools');\n  url.searchParams.append('page', String(params.current));\n  url.searchParams.append('size', String(params.pageSize));\n  if (params.category) url.searchParams.append('category', params.category);\n  if (params.keyword) url.searchParams.append('keyword', params.keyword);\n  const res = await fetch(url.toString());\n  if (!res.ok) throw new Error('网络错误');\n  const data = await res.json();\n  if (data.success) return { list: data.data, total: data.total };\n  throw new Error(data.message || '获取工具失败');\n}\n\nasync function fetchCategoriesService() {\n  const res = await fetch('http://localhost:8080/api/public/categories');\n  if (!res.ok) throw new Error('网络错误');\n  const data = await res.json();\n  if (data.success) return data.data;\n  throw new Error(data.message || '获取分类失败');\n}\n\nconst DisplayIndex: React.FC = () => {\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [searchKeyword, setSearchKeyword] = useState('');\n\n  const { data: categories = [] } = useRequest(fetchCategoriesService);\n  const { data, loading, pagination } = useRequest(\n    ({ current, pageSize }) => fetchToolsService({ current, pageSize, category: selectedCategory, keyword: searchKeyword }),\n    {\n      paginated: true,\n      refreshDeps: [selectedCategory, searchKeyword],\n    }\n  );\n\n  const getCategoryName = (key: string) => {\n    const category = categories.find((cat: any) => cat.key === key);\n    return category ? category.name : key;\n  };\n\n  return (\n    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>\n      <div style={{ textAlign: 'center', marginBottom: '48px' }}>\n        <Title level={1}>inCODE 工具链平台</Title>\n        <Paragraph style={{ fontSize: '18px', color: '#666' }}>专业的开发工具链展示平台</Paragraph>\n      </div>\n\n      <div style={{ marginBottom: '32px' }}>\n        <Row gutter={16} align=\"middle\">\n          <Col xs={24} md={12}>\n            <Search placeholder=\"搜索工具...\" onSearch={setSearchKeyword} size=\"large\" />\n          </Col>\n          <Col xs={24} md={6}>\n            <Select placeholder=\"选择分类\" value={selectedCategory} onChange={setSelectedCategory} style={{ width: '100%' }} size=\"large\" allowClear>\n              {categories.map((cat: any) => <Option key={cat.key} value={cat.key}>{cat.name} ({cat.count})</Option>)}\n            </Select>\n          </Col>\n        </Row>\n      </div>\n\n      <Spin spinning={loading}>\n        <Row gutter={[24, 24]}>\n          {(data?.list || []).map((tool: Tool) => (\n            <Col key={tool.id} xs={24} sm={12} lg={8}>\n              <ToolCard tool={tool} getCategoryName={getCategoryName} />\n            </Col>\n          ))}\n        </Row>\n\n        {data?.list.length === 0 && !loading && (\n          <div style={{ textAlign: 'center', padding: '48px', color: '#999' }}>\n            暂无工具\n          </div>\n        )}\n      </Spin>\n\n      {data?.total > 0 && (\n        <div style={{ textAlign: 'center', marginTop: '48px' }}>\n          <Pagination {...pagination} showSizeChanger showQuickJumper showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`} />\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default DisplayIndex;", "__isJSFile": true, "__absFile": "/Users/<USER>/WebstormProjects/inCODE/ui/src/pages/display/index.tsx"}, "admin/Login": {"path": "admin/Login", "id": "admin/Login", "parentId": "@@/global-layout", "file": "admin/Login.tsx", "absPath": "/admin/Login", "__content": "import React from 'react';\nimport { Form, Input, Button, Card, Typography, message, Checkbox } from 'antd';\nimport { UserOutlined, LockOutlined } from '@ant-design/icons';\nimport { history } from 'umi';\nimport { useRequest } from 'ahooks';\n\nconst { Title, Paragraph } = Typography;\n\ninterface LoginForm {\n  username: string;\n  password: string;\n  remember: boolean;\n}\n\n// 1. 封装登录请求服务\n// 这是一个独立的函数，负责调用 API\n// useRequest 会将表单数据作为第一个参数传递给它\nasync function loginService(values: Pick<LoginForm, 'username' | 'password'>) {\n  // 注意：这里我们假设有一个代理或直接可访问的后端服务\n  // 在实际项目中，你可能需要配置 Umi 的代理来解决跨域问题\n  const response = await fetch('http://localhost:8080/api/admin/login', {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    body: JSON.stringify(values),\n  });\n\n  if (!response.ok) {\n    // 如果 HTTP 状态码不是 2xx，也认为是错误\n    const errorData = await response.json().catch(() => ({})); // 尝试解析错误信息\n    throw new Error(errorData.message || '网络请求失败');\n  }\n\n  const data = await response.json();\n\n  if (data.success) {\n    return data; // 成功时，返回整个数据对象\n  } else {\n    // 如果业务上失败，也抛出错误\n    throw new Error(data.message || '登录失败');\n  }\n}\n\nconst AdminLogin: React.FC = () => {\n\n  // 2. 使用 useRequest 管理登录逻辑\n  const { loading, run: runLogin } = useRequest(loginService, {\n    manual: true, // 设置为手动触发，不在组件加载时自动执行\n    onSuccess: (result, params) => {\n      // 登录成功后的回调\n      localStorage.setItem('admin_token', result.token);\n      localStorage.setItem('admin_user', JSON.stringify(result.user));\n      message.success('登录成功！');\n      history.push('/admin/dashboard');\n    },\n    onError: (error) => {\n      // 登录失败后的回调\n      message.error(error.message);\n      history.push('/admin/dashboard'); // 即使失败也跳转，与原逻辑保持一致\n    },\n  });\n\n  // 3. 表单提交时，调用 runLogin 来触发请求\n  const onFinish = (values: LoginForm) => {\n    runLogin({\n      username: values.username,\n      password: values.password,\n    });\n  };\n\n  return (\n    <div style={{\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      padding: '20px'\n    }}>\n      <Card\n        style={{\n          width: '100%',\n          maxWidth: '400px',\n          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',\n          borderRadius: '12px'\n        }}\n      >\n        <div style={{ textAlign: 'center', marginBottom: '32px' }}>\n          <Title level={2} style={{ color: '#1890ff', marginBottom: '8px' }}>\n            管理后台\n          </Title>\n          <Paragraph type=\"secondary\">\n            inCODE 工具链平台管理系统\n          </Paragraph>\n        </div>\n\n        <Form\n          name=\"admin_login\"\n          onFinish={onFinish}\n          autoComplete=\"off\"\n          size=\"large\"\n        >\n          <Form.Item\n            name=\"username\"\n            rules={[\n              { required: true, message: '请输入用户名!' },\n              { min: 3, message: '用户名至少3个字符!' }\n            ]}\n          >\n            <Input\n              prefix={<UserOutlined />}\n              placeholder=\"用户名\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"password\"\n            rules={[\n              { required: true, message: '请输入密码!' },\n              { min: 6, message: '密码至少6个字符!' }\n            ]}\n          >\n            <Input.Password\n              prefix={<LockOutlined />}\n              placeholder=\"密码\"\n            />\n          </Form.Item>\n\n          <Form.Item name=\"remember\" valuePropName=\"checked\">\n            <Checkbox>记住我</Checkbox>\n          </Form.Item>\n\n          <Form.Item>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={loading} // loading 状态由 useRequest 自动管理\n              style={{ width: '100%', height: '44px' }}\n            >\n              登录\n            </Button>\n          </Form.Item>\n        </Form>\n\n        <div style={{ textAlign: 'center', marginTop: '24px' }}>\n          <Paragraph type=\"secondary\" style={{ fontSize: '12px' }}>\n            默认账号：admin / 123456\n          </Paragraph>\n        </div>\n      </Card>\n    </div>\n  );\n};\n\nexport default AdminLogin;", "__isJSFile": true, "__absFile": "/Users/<USER>/WebstormProjects/inCODE/ui/src/pages/admin/Login.tsx"}, "index": {"path": "/", "id": "index", "parentId": "@@/global-layout", "file": "index.tsx", "absPath": "/", "__content": "import { Redirect } from 'umi';\n\nexport default () => {\n  return <Redirect to=\"/display\" />;\n};\n", "__isJSFile": true, "__absFile": "/Users/<USER>/WebstormProjects/inCODE/ui/src/pages/index.tsx"}, "@@/global-layout": {"id": "@@/global-layout", "path": "/", "file": "/Users/<USER>/WebstormProjects/inCODE/ui/src/layouts/index.tsx", "absPath": "/", "isLayout": true, "__absFile": "/Users/<USER>/WebstormProjects/inCODE/ui/src/layouts/index.tsx"}}, "apiRoutes": {}, "hasSrcDir": true, "npmClient": "npm", "umi": {"version": "4.4.11", "name": "<PERSON><PERSON>", "importSource": "umi", "cliName": "umi"}, "bundleStatus": {"done": false}, "mfsuBundleStatus": {"done": false}, "react": {"version": "18.3.1", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/react"}, "react-dom": {"version": "18.3.1", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/react-dom"}, "appJS": {"path": "/Users/<USER>/WebstormProjects/inCODE/ui/src/app.ts", "exports": ["getInitialState"]}, "locale": "zh-CN", "vite": {}, "globalCSS": [], "globalJS": [], "overridesCSS": [], "bundler": "vite", "git": {"originUrl": "https://github.com/sangsangde4/inCODE.git"}, "framework": "react", "typescript": {"tsVersion": "5.8.3", "tslibVersion": "2.8.1"}, "faviconFiles": [], "port": 8000, "host": "0.0.0.0", "ip": "*************", "antd": {"pkgPath": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/antd", "version": "5.21.6"}}