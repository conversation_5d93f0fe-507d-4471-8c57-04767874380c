{"cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "pkg": {"name": "ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "umi dev", "build": "umi build", "setup": "umi setup", "start": "npm run dev"}, "dependencies": {"@umijs/max": "^4.4.2", "ahooks": "^3.8.0", "antd": "^5.21.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^10.1.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "typescript": "^5.0.2", "vite": "^4.4.5"}}, "pkgPath": "/Users/<USER>/WebstormProjects/inCODE/ui/package.json", "plugins": {"./node_modules/@umijs/core/dist/service/servicePlugin": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "preset", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/core/dist/service/servicePlugin.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/core/dist/service/servicePlugin", "key": "servicePlugin"}, "@umijs/preset-umi": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "preset", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/index.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "@umijs/preset-umi", "key": "umi"}, "./node_modules/@umijs/preset-umi/dist/registerMethods": {"config": {}, "time": {"hooks": {"onStart": [0]}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/registerMethods.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/registerMethods", "key": "registerMethods"}, "@umijs/did-you-know": {"config": {}, "time": {"hooks": {"onStart": [1]}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/did-you-know/dist/plugin.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "@umijs/did-you-know", "key": "umijsDidYouKnow"}, "./node_modules/@umijs/preset-umi/dist/features/404/404": {"config": {}, "time": {"hooks": {"modifyRoutes": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/404/404.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/404/404", "key": "404"}, "./node_modules/@umijs/preset-umi/dist/features/appData/appData": {"config": {}, "time": {"hooks": {"modifyAppData": [29]}, "register": 10}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/appData/appData.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/appData/appData", "key": "appData"}, "./node_modules/@umijs/preset-umi/dist/features/appData/umiInfo": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/appData/umiInfo.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/appData/umiInfo", "key": "umiInfo"}, "./node_modules/@umijs/preset-umi/dist/features/check/check": {"config": {}, "time": {"hooks": {"onCheckConfig": [0], "onCheck": [0]}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/check/check.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/check/check", "key": "check"}, "./node_modules/@umijs/preset-umi/dist/features/check/babel722": {"config": {}, "time": {"hooks": {"onCheck": [0]}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/check/babel722.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/check/babel722", "key": "babel722"}, "./node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting", "key": "codeSplitting"}, "./node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins": {"config": {}, "time": {"hooks": {"modifyConfig": [0]}, "register": 4}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins", "key": "configPlugins"}, "virtual: config-title": {"id": "virtual: config-title", "key": "title", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-styles": {"id": "virtual: config-styles", "key": "styles", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-scripts": {"id": "virtual: config-scripts", "key": "scripts", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-routes": {"id": "virtual: config-routes", "key": "routes", "config": {"onChange": "regenerateTmpFiles"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-routeLoader": {"id": "virtual: config-routeLoader", "key": "routeLoader", "config": {"default": {"moduleType": "esm"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-reactRouter5Compat": {"id": "virtual: config-reactRouter5Compat", "key": "reactRouter5Compat", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-presets": {"id": "virtual: config-presets", "key": "presets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-plugins": {"id": "virtual: config-plugins", "key": "plugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-npmClient": {"id": "virtual: config-npmClient", "key": "npmClient", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mountElementId": {"id": "virtual: config-mountElementId", "key": "mountElementId", "config": {"default": "root"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-metas": {"id": "virtual: config-metas", "key": "metas", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-links": {"id": "virtual: config-links", "key": "links", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-historyWithQuery": {"id": "virtual: config-historyWithQuery", "key": "historyWithQuery", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-history": {"id": "virtual: config-history", "key": "history", "config": {"default": {"type": "browser"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-headScripts": {"id": "virtual: config-headScripts", "key": "headScripts", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-esbuildMinifyIIFE": {"id": "virtual: config-esbuildMinifyIIFE", "key": "esbuildMinifyIIFE", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-conventionRoutes": {"id": "virtual: config-conventionRoutes", "key": "conventionRoutes", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-conventionLayout": {"id": "virtual: config-conventionLayout", "key": "conventionLayout", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-base": {"id": "virtual: config-base", "key": "base", "config": {"default": "/"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-analyze": {"id": "virtual: config-analyze", "key": "analyze", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-writeToDisk": {"id": "virtual: config-writeToDisk", "key": "writeToDisk", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-transformRuntime": {"id": "virtual: config-transformRuntime", "key": "transformRuntime", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-theme": {"id": "virtual: config-theme", "key": "theme", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-targets": {"id": "virtual: config-targets", "key": "targets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-svgr": {"id": "virtual: config-svgr", "key": "svgr", "config": {"default": {}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-svgo": {"id": "virtual: config-svgo", "key": "svgo", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-stylusLoader": {"id": "virtual: config-stylusLoader", "key": "stylus<PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-styleLoader": {"id": "virtual: config-style<PERSON>oader", "key": "<PERSON><PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-srcTranspilerOptions": {"id": "virtual: config-srcTranspilerOptions", "key": "srcTranspilerOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-srcTranspiler": {"id": "virtual: config-srcTranspiler", "key": "srcTranspiler", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-sassLoader": {"id": "virtual: config-sassLoader", "key": "sass<PERSON><PERSON>der", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-runtimePublicPath": {"id": "virtual: config-runtimePublicPath", "key": "runtimePublicPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-purgeCSS": {"id": "virtual: config-purgeCSS", "key": "purgeCSS", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-publicPath": {"id": "virtual: config-publicPath", "key": "publicPath", "config": {"default": "/"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-proxy": {"id": "virtual: config-proxy", "key": "proxy", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-postcssLoader": {"id": "virtual: config-postcssLoader", "key": "postcss<PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-outputPath": {"id": "virtual: config-outputPath", "key": "outputPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-normalCSSLoaderModules": {"id": "virtual: config-normalCSSLoaderModules", "key": "normalCSSLoaderModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mfsu": {"id": "virtual: config-mfsu", "key": "mfsu", "config": {"default": {"strategy": "eager"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mdx": {"id": "virtual: config-mdx", "key": "mdx", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-manifest": {"id": "virtual: config-manifest", "key": "manifest", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-lessLoader": {"id": "virtual: config-less<PERSON><PERSON>der", "key": "<PERSON><PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-jsMinifierOptions": {"id": "virtual: config-jsMinifierOptions", "key": "jsMinifierOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-jsMinifier": {"id": "virtual: config-jsMinifier", "key": "jsMinifier", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-inlineLimit": {"id": "virtual: config-inlineLimit", "key": "inlineLimit", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-ignoreMomentLocale": {"id": "virtual: config-ignoreMomentLocale", "key": "ignoreMomentLocale", "config": {"default": true}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-https": {"id": "virtual: config-https", "key": "https", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-hash": {"id": "virtual: config-hash", "key": "hash", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-forkTSChecker": {"id": "virtual: config-fork<PERSON><PERSON><PERSON><PERSON>", "key": "forkTSChecker", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-fastRefresh": {"id": "virtual: config-fastRefresh", "key": "fastRefresh", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraPostCSSPlugins": {"id": "virtual: config-extraPostCSSPlugins", "key": "extraPostCSSPlugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelPresets": {"id": "virtual: config-extraBabelPresets", "key": "extraBabelPresets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelPlugins": {"id": "virtual: config-extraBabelPlugins", "key": "extraBabelPlugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelIncludes": {"id": "virtual: config-extraBabelIncludes", "key": "extraBabelIncludes", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-externals": {"id": "virtual: config-externals", "key": "externals", "config": {"default": {}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-esm": {"id": "virtual: config-esm", "key": "esm", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-devtool": {"id": "virtual: config-devtool", "key": "devtool", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-depTranspiler": {"id": "virtual: config-depTranspiler", "key": "depTranspiler", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-define": {"id": "virtual: config-define", "key": "define", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-deadCode": {"id": "virtual: config-deadCode", "key": "deadCode", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssPublicPath": {"id": "virtual: config-cssPublicPath", "key": "cssPublicPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssMinifierOptions": {"id": "virtual: config-cssMinifierOptions", "key": "cssMinifierOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssMinifier": {"id": "virtual: config-cssMinifier", "key": "cssMinifier", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssLoaderModules": {"id": "virtual: config-cssLoaderModules", "key": "cssLoaderModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssLoader": {"id": "virtual: config-cssLoader", "key": "cssL<PERSON>der", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-copy": {"id": "virtual: config-copy", "key": "copy", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-checkDepCssModules": {"id": "virtual: config-checkDepCssModules", "key": "checkDepCssModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-chainWebpack": {"id": "virtual: config-chainWebpack", "key": "chainWebpack", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cacheDirectoryPath": {"id": "virtual: config-cacheDirectoryPath", "key": "cacheDirectoryPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-babelLoaderCustomize": {"id": "virtual: config-babelLoaderCustomize", "key": "babelLoaderCustomize", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-autoprefixer": {"id": "virtual: config-autoprefixer", "key": "autoprefixer", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-autoCSSModules": {"id": "virtual: config-autoCSSModules", "key": "autoCSSModules", "config": {"default": true}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-alias": {"id": "virtual: config-alias", "key": "alias", "config": {"default": {"umi": "@@/exports", "react": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/react", "react-dom": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/react-dom", "react-router": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/node_modules/react-router", "react-router-dom": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/node_modules/react-router-dom"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "./node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin", "key": "crossorigin"}, "./node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand": {"config": {}, "time": {"hooks": {"onStart": [0]}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand", "key": "deps<PERSON>n<PERSON><PERSON><PERSON>"}, "./node_modules/@umijs/preset-umi/dist/features/devTool/devTool": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/devTool/devTool.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/devTool/devTool", "key": "devTool"}, "./node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker": {"config": {}, "time": {"hooks": {}, "register": 39}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker", "key": "esbuildHelperChecker"}, "./node_modules/@umijs/preset-umi/dist/features/esmi/esmi": {"config": {}, "time": {"hooks": {}, "register": 37}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/esmi/esmi.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/esmi/esmi", "key": "esmi"}, "./node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic": {"config": {}, "time": {"hooks": {}, "register": 9}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic", "key": "exportStatic"}, "./node_modules/@umijs/preset-umi/dist/features/favicons/favicons": {"config": {}, "time": {"hooks": {"modifyAppData": [1]}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/favicons/favicons.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/favicons/favicons", "key": "favicons"}, "./node_modules/@umijs/preset-umi/dist/features/helmet/helmet": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/helmet/helmet.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/helmet/helmet", "key": "helmet"}, "./node_modules/@umijs/preset-umi/dist/features/icons/icons": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/icons/icons.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/icons/icons", "key": "icons"}, "./node_modules/@umijs/preset-umi/dist/features/mock/mock": {"config": {}, "time": {"hooks": {"onStart": [1]}, "register": 15}, "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/mock/mock.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/mock/mock", "key": "mock"}, "./node_modules/@umijs/preset-umi/dist/features/mpa/mpa": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/mpa/mpa.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/mpa/mpa", "key": "mpa"}, "./node_modules/@umijs/preset-umi/dist/features/okam/okam": {"config": {}, "time": {"hooks": {}, "register": 0}, "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/okam/okam.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/okam/okam", "key": "okam"}, "./node_modules/@umijs/preset-umi/dist/features/overrides/overrides": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/overrides/overrides.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/overrides/overrides", "key": "overrides"}, "./node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency", "key": "phantomDependency"}, "./node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill": {"config": {}, "time": {"hooks": {"modifyConfig": [1]}, "register": 1}, "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill", "key": "polyfill"}, "./node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill", "key": "publicPathPolyfill"}, "./node_modules/@umijs/preset-umi/dist/features/prepare/prepare": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/prepare/prepare.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/prepare/prepare", "key": "prepare"}, "./node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch", "key": "routePrefetch"}, "./node_modules/@umijs/preset-umi/dist/features/terminal/terminal": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/terminal/terminal.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/terminal/terminal", "key": "terminal"}, "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles", "key": "tmpFiles"}, "./node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader", "key": "clientLoader"}, "./node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps": {"config": {}, "time": {"hooks": {}, "register": 1}, "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps", "key": "routeProps"}, "./node_modules/@umijs/preset-umi/dist/features/ssr/ssr": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/ssr/ssr.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/ssr/ssr", "key": "ssr"}, "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes", "key": "configTypes"}, "./node_modules/@umijs/preset-umi/dist/features/transform/transform": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/transform/transform.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/transform/transform", "key": "transform"}, "./node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport", "key": "lowImport"}, "./node_modules/@umijs/preset-umi/dist/features/vite/vite": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/vite/vite.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/vite/vite", "key": "vite"}, "./node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute": {"config": {}, "time": {"hooks": {}, "register": 2}, "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute", "key": "apiRoute"}, "./node_modules/@umijs/preset-umi/dist/features/monorepo/redirect": {"config": {}, "time": {"hooks": {}, "register": 10}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/monorepo/redirect.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/monorepo/redirect", "key": "monorepoRedirect"}, "./node_modules/@umijs/preset-umi/dist/features/test/test": {"config": {}, "time": {"hooks": {}, "register": 0}, "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/test/test.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/test/test", "key": "test"}, "./node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent", "key": "clickToComponent"}, "./node_modules/@umijs/preset-umi/dist/features/legacy/legacy": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/legacy/legacy.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/legacy/legacy", "key": "legacy"}, "./node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose", "key": "classPropertiesLoose"}, "./node_modules/@umijs/preset-umi/dist/features/webpack/webpack": {"config": {}, "time": {"hooks": {}, "register": 0}, "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/webpack/webpack.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/webpack/webpack", "key": "preset-umi:webpack"}, "./node_modules/@umijs/preset-umi/dist/features/swc/swc": {"config": {}, "time": {"hooks": {"addOnDemandDeps": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/swc/swc.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/swc/swc", "key": "swc"}, "./node_modules/@umijs/preset-umi/dist/features/ui/ui": {"config": {}, "time": {"hooks": {}, "register": 1}, "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/ui/ui.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/ui/ui", "key": "ui"}, "./node_modules/@umijs/preset-umi/dist/features/mako/mako": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/mako/mako.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/mako/mako", "key": "mako"}, "./node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian": {"config": {}, "time": {"hooks": {}, "register": 1}, "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian", "key": "hm<PERSON><PERSON><PERSON><PERSON>"}, "./node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad": {"config": {}, "time": {"hooks": {}, "register": 1}, "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad", "key": "routePreloadOnLoad"}, "./node_modules/@umijs/preset-umi/dist/features/forget/forget": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/forget/forget.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/forget/forget", "key": "forget"}, "./node_modules/@umijs/preset-umi/dist/features/bundler/bundler": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/features/bundler/bundler.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/features/bundler/bundler", "key": "preset-umi:bundler"}, "./node_modules/@umijs/preset-umi/dist/commands/build": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/build.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/build", "key": "build"}, "./node_modules/@umijs/preset-umi/dist/commands/config/config": {"config": {}, "time": {"hooks": {}, "register": 18}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/config/config.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/config/config", "key": "config"}, "./node_modules/@umijs/preset-umi/dist/commands/dev/dev": {"config": {}, "time": {"hooks": {"modifyAppData": [4], "onStart": [0]}, "register": 14}, "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/dev/dev.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/dev/dev", "key": "dev"}, "./node_modules/@umijs/preset-umi/dist/commands/help": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/help.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/help", "key": "help"}, "./node_modules/@umijs/preset-umi/dist/commands/lint": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/lint.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/lint", "key": "lint"}, "./node_modules/@umijs/preset-umi/dist/commands/setup": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/setup.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/setup", "key": "setup"}, "./node_modules/@umijs/preset-umi/dist/commands/deadcode": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/deadcode.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/deadcode", "key": "deadcode"}, "./node_modules/@umijs/preset-umi/dist/commands/version": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/version.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/version", "key": "version"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/page": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/generators/page.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/page", "key": "generator:page"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/prettier": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/generators/prettier.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/prettier", "key": "generator:prettier"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig", "key": "generator:tsconfig"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/jest": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/generators/jest.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/jest", "key": "generator:jest"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss", "key": "generator:tailwindcss"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/dva": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/generators/dva.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/dva", "key": "generator:dva"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/component": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/generators/component.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/component", "key": "generator:component"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/mock": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/generators/mock.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/mock", "key": "generator:mock"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/cypress": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/generators/cypress.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/cypress", "key": "generator:cypress"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/api": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/generators/api.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/api", "key": "generator:api"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/precommit": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/generators/precommit.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/precommit", "key": "generator:precommit"}, "./node_modules/@umijs/preset-umi/dist/commands/plugin": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/plugin.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/plugin", "key": "command:plugin"}, "./node_modules/@umijs/preset-umi/dist/commands/verify-commit": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/verify-commit.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/verify-commit", "key": "verifyCommit"}, "./node_modules/@umijs/preset-umi/dist/commands/preview": {"config": {}, "time": {"hooks": {}, "register": 12}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/preview.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/preview", "key": "preview"}, "./node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu", "key": "mfsu-cli"}, "@umijs/plugin-run": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/plugin-run/dist/index.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "@umijs/plugin-run", "key": "run"}, "./node_modules/@umijs/core/dist/service/generatePlugin": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/core/dist/service/generatePlugin.js", "cwd": "/Users/<USER>/WebstormProjects/inCODE/ui", "id": "./node_modules/@umijs/core/dist/service/generatePlugin", "key": "generatePlugin"}}, "presets": [], "name": "dev", "args": {"_": []}, "userConfig": {"npmClient": "npm", "alias": {"@": "/src"}}, "mainConfigFile": "/Users/<USER>/WebstormProjects/inCODE/ui/.umirc.ts", "config": {"routeLoader": {"moduleType": "esm"}, "mountElementId": "root", "history": {"type": "browser"}, "base": "/", "svgr": {}, "publicPath": "/", "mfsu": {"strategy": "eager"}, "ignoreMomentLocale": true, "externals": {}, "autoCSSModules": true, "alias": {"umi": "@@/exports", "react": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/react", "react-dom": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/react-dom", "react-router": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/node_modules/react-router", "react-router-dom": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/@umijs/preset-umi/node_modules/react-router-dom", "@": "/Users/<USER>/WebstormProjects/inCODE/ui/src", "@@": "/Users/<USER>/WebstormProjects/inCODE/ui/src/.umi", "regenerator-runtime": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/regenerator-runtime"}, "npmClient": "npm", "targets": {"chrome": 80}}, "routes": {"admin/Dashboard": {"path": "admin/Dashboard", "id": "admin/Dashboard", "parentId": "@@/global-layout", "file": "admin/Dashboard.tsx", "absPath": "/admin/Dashboard", "__content": "import React, { useState, useEffect } from 'react';\nimport { Layout, Menu, Card, Row, Col, Statistic, Table, Tag, Button, Avatar, Dropdown, Space } from 'antd';\nimport {\n  DashboardOutlined,\n  ToolOutlined,\n  FileTextOutlined,\n  BookOutlined,\n  UserOutlined,\n  LogoutOutlined,\n  SettingOutlined,\n  PlusOutlined,\n  EyeOutlined,\n  EditOutlined,\n  DeleteOutlined\n} from '@ant-design/icons';\nimport { history } from 'umi';\n\nconst { Header, Sider, Content } = Layout;\n\nconst AdminDashboard: React.FC = () => {\n  const [collapsed, setCollapsed] = useState(false);\n  const [currentMenu, setCurrentMenu] = useState('dashboard');\n  const [user, setUser] = useState<any>(null);\n  const [stats, setStats] = useState({\n    toolCount: 0,\n    changelogCount: 0,\n    docCount: 0,\n    viewCount: 0\n  });\n  const navigate = history.push;\n\n  useEffect(() => {\n    // 获取用户信息\n    const userInfo = localStorage.getItem('admin_user');\n    if (userInfo) {\n      setUser(JSON.parse(userInfo));\n    } else {\n      navigate('/admin/login');\n    }\n\n    // 获取统计数据\n    fetchStats();\n  }, []);\n\n  const fetchStats = async () => {\n    try {\n      // 这里应该调用实际的统计API\n      setStats({\n        toolCount: 12,\n        changelogCount: 45,\n        docCount: 28,\n        viewCount: 15420\n      });\n    } catch (error) {\n      console.error('获取统计数据失败:', error);\n    }\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem('admin_token');\n    localStorage.removeItem('admin_user');\n    navigate('/admin/login');\n  };\n\n  const userMenu = (\n    <Menu>\n      <Menu.Item key=\"profile\" icon={<UserOutlined />}>\n        个人资料\n      </Menu.Item>\n      <Menu.Item key=\"settings\" icon={<SettingOutlined />}>\n        设置\n      </Menu.Item>\n      <Menu.Divider />\n      <Menu.Item key=\"logout\" icon={<LogoutOutlined />} onClick={handleLogout}>\n        退出登录\n      </Menu.Item>\n    </Menu>\n  );\n\n  const sidebarMenu = [\n    {\n      key: 'dashboard',\n      icon: <DashboardOutlined />,\n      label: '仪表板'\n    },\n    {\n      key: 'tools',\n      icon: <ToolOutlined />,\n      label: '工具管理'\n    },\n    {\n      key: 'changelogs',\n      icon: <FileTextOutlined />,\n      label: '更新日志'\n    },\n    {\n      key: 'help-docs',\n      icon: <BookOutlined />,\n      label: '帮助文档'\n    }\n  ];\n\n  const renderDashboardContent = () => (\n    <div>\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: '24px' }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"工具总数\"\n              value={stats.toolCount}\n              prefix={<ToolOutlined />}\n              valueStyle={{ color: '#3f8600' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"更新日志\"\n              value={stats.changelogCount}\n              prefix={<FileTextOutlined />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"帮助文档\"\n              value={stats.docCount}\n              prefix={<BookOutlined />}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"总浏览量\"\n              value={stats.viewCount}\n              prefix={<EyeOutlined />}\n              valueStyle={{ color: '#cf1322' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 最近活动 */}\n      <Row gutter={16}>\n        <Col span={12}>\n          <Card title=\"最新工具\" extra={<Button type=\"link\">查看全部</Button>}>\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>\n              {[\n                { name: 'Code Analyzer', version: '2.1.0', status: 'active' },\n                { name: 'API Tester', version: '1.5.2', status: 'active' },\n                { name: 'Deploy Helper', version: '3.0.1', status: 'active' }\n              ].map((tool, index) => (\n                <div key={index} style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                  <div>\n                    <div style={{ fontWeight: 'bold' }}>{tool.name}</div>\n                    <div style={{ color: '#666', fontSize: '12px' }}>版本 {tool.version}</div>\n                  </div>\n                  <Tag color=\"green\">活跃</Tag>\n                </div>\n              ))}\n            </div>\n          </Card>\n        </Col>\n        <Col span={12}>\n          <Card title=\"最新更新日志\" extra={<Button type=\"link\">查看全部</Button>}>\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>\n              {[\n                { title: '新增代码质量评分功能', version: '2.1.0', date: '2024-01-15' },\n                { title: '修复内存泄漏问题', version: '2.0.5', date: '2024-01-10' },\n                { title: '支持GraphQL协议测试', version: '1.5.2', date: '2024-01-08' }\n              ].map((log, index) => (\n                <div key={index}>\n                  <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>{log.title}</div>\n                  <div style={{ color: '#666', fontSize: '12px' }}>\n                    版本 {log.version} • {log.date}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </Card>\n        </Col>\n      </Row>\n    </div>\n  );\n\n  const renderContent = () => {\n    switch (currentMenu) {\n      case 'dashboard':\n        return renderDashboardContent();\n      case 'tools':\n        return <div>工具管理页面（待实现）</div>;\n      case 'changelogs':\n        return <div>更新日志管理页面（待实现）</div>;\n      case 'help-docs':\n        return <div>帮助文档管理页面（待实现）</div>;\n      default:\n        return renderDashboardContent();\n    }\n  };\n\n  return (\n    <Layout style={{ minHeight: '100vh' }}>\n      <Sider trigger={null} collapsible collapsed={collapsed}>\n        <div style={{\n          height: '64px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: 'white',\n          fontSize: '18px',\n          fontWeight: 'bold'\n        }}>\n          {collapsed ? 'iC' : 'inCODE'}\n        </div>\n        <Menu\n          theme=\"dark\"\n          mode=\"inline\"\n          selectedKeys={[currentMenu]}\n          items={sidebarMenu}\n          onClick={({ key }) => setCurrentMenu(key)}\n        />\n      </Sider>\n      \n      <Layout>\n        <Header style={{\n          padding: '0 24px',\n          background: '#fff',\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          boxShadow: '0 1px 4px rgba(0,21,41,.08)'\n        }}>\n          <Button\n            type=\"text\"\n            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n            onClick={() => setCollapsed(!collapsed)}\n            style={{ fontSize: '16px', width: 64, height: 64 }}\n          />\n          \n          <Space>\n            <span>欢迎回来，{user?.realName || user?.username}</span>\n            <Dropdown overlay={userMenu} placement=\"bottomRight\">\n              <Avatar\n                style={{ backgroundColor: '#1890ff', cursor: 'pointer' }}\n                icon={<UserOutlined />}\n              />\n            </Dropdown>\n          </Space>\n        </Header>\n        \n        <Content style={{\n          margin: '24px',\n          padding: '24px',\n          background: '#fff',\n          borderRadius: '6px'\n        }}>\n          {renderContent()}\n        </Content>\n      </Layout>\n    </Layout>\n  );\n};\n\nexport default AdminDashboard;\n", "__isJSFile": true, "__absFile": "/Users/<USER>/WebstormProjects/inCODE/ui/src/pages/admin/Dashboard.tsx"}, "display/about": {"path": "display/about", "id": "display/about", "parentId": "@@/global-layout", "file": "display/about.tsx", "absPath": "/display/about", "__content": "import React from 'react';\nimport { Typography, Card } from 'antd';\n\nconst { Title, Paragraph } = Typography;\n\nconst AboutPage: React.FC = () => {\n  return (\n    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>\n      <Card>\n        <Title level={2}>关于 inCODE 工具链平台</Title>\n        <Paragraph>\n          inCODE 工具链平台致力于为开发者提供一站式的工具发现、管理和使用体验。\n          我们汇集了各类开发工具，包括代码分析、测试、部署、文档生成等，旨在提升开发效率和项目质量。\n        </Paragraph>\n        <Paragraph>\n          我们的愿景是构建一个开放、共享的开发者社区，让优秀的工具能够被更多人发现和利用。\n        </Paragraph>\n        <Title level={3}>联系我们</Title>\n        <Paragraph>\n          如果您有任何疑问、建议或合作意向，请随时通过以下方式联系我们：\n        </Paragraph>\n        <ul>\n          <li>邮箱：<a href=\"mailto:<EMAIL>\"><EMAIL></a></li>\n          <li>GitHub：<a href=\"https://github.com/inCODE\" target=\"_blank\" rel=\"noopener noreferrer\">github.com/inCODE</a></li>\n        </ul>\n      </Card>\n    </div>\n  );\n};\n\nexport default AboutPage;\n", "__isJSFile": true, "__absFile": "/Users/<USER>/WebstormProjects/inCODE/ui/src/pages/display/about.tsx"}, "display/index": {"path": "display", "id": "display/index", "parentId": "@@/global-layout", "file": "display/index.tsx", "absPath": "/display", "__content": "import React, { useState } from 'react';\nimport { Row, Col, Typography, Space, Input, Select, Pagination, Spin } from 'antd';\nimport { useRequest } from 'ahooks';\nimport ToolCard from '@/components/ToolCard'; // 导入 ToolCard 组件\n\nconst { Title, Paragraph } = Typography;\nconst { Search } = Input;\nconst { Option } = Select;\n\ninterface Tool {\n  id: number;\n  name: string;\n  title: string;\n  description: string;\n  logoUrl?: string;\n  version: string;\n  category: string;\n  tags: string[];\n  githubUrl?: string;\n  downloadUrl?: string;\n  documentationUrl?: string;\n  viewCount: number;\n  updatedAt: string;\n}\n\n// 封装服务\nasync function fetchToolsService(params: { category?: string; keyword?: string; current: number; pageSize: number }) {\n  const url = new URL('http://localhost:8080/api/public/tools');\n  url.searchParams.append('page', String(params.current));\n  url.searchParams.append('size', String(params.pageSize));\n  if (params.category) url.searchParams.append('category', params.category);\n  if (params.keyword) url.searchParams.append('keyword', params.keyword);\n  const res = await fetch(url.toString());\n  if (!res.ok) throw new Error('网络错误');\n  const data = await res.json();\n  if (data.success) return { list: data.data, total: data.total };\n  throw new Error(data.message || '获取工具失败');\n}\n\nasync function fetchCategoriesService() {\n  const res = await fetch('http://localhost:8080/api/public/categories');\n  if (!res.ok) throw new Error('网络错误');\n  const data = await res.json();\n  if (data.success) return data.data;\n  throw new Error(data.message || '获取分类失败');\n}\n\nconst DisplayIndex: React.FC = () => {\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [searchKeyword, setSearchKeyword] = useState('');\n\n  const { data: categories = [] } = useRequest(fetchCategoriesService);\n  const { data, loading, pagination } = useRequest(\n    ({ current, pageSize }) => fetchToolsService({ current, pageSize, category: selectedCategory, keyword: searchKeyword }),\n    {\n      paginated: true,\n      refreshDeps: [selectedCategory, searchKeyword],\n    }\n  );\n\n  const getCategoryName = (key: string) => {\n    const category = categories.find((cat: any) => cat.key === key);\n    return category ? category.name : key;\n  };\n\n  return (\n    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>\n      <div style={{ textAlign: 'center', marginBottom: '48px' }}>\n        <Title level={1}>inCODE 工具链平台</Title>\n        <Paragraph style={{ fontSize: '18px', color: '#666' }}>专业的开发工具链展示平台</Paragraph>\n      </div>\n\n      <div style={{ marginBottom: '32px' }}>\n        <Row gutter={16} align=\"middle\">\n          <Col xs={24} md={12}>\n            <Search placeholder=\"搜索工具...\" onSearch={setSearchKeyword} size=\"large\" />\n          </Col>\n          <Col xs={24} md={6}>\n            <Select placeholder=\"选择分类\" value={selectedCategory} onChange={setSelectedCategory} style={{ width: '100%' }} size=\"large\" allowClear>\n              {categories.map((cat: any) => <Option key={cat.key} value={cat.key}>{cat.name} ({cat.count})</Option>)}\n            </Select>\n          </Col>\n        </Row>\n      </div>\n\n      <Spin spinning={loading}>\n        <Row gutter={[24, 24]}>\n          {(data?.list || []).map((tool: Tool) => (\n            <Col key={tool.id} xs={24} sm={12} lg={8}>\n              <ToolCard tool={tool} getCategoryName={getCategoryName} />\n            </Col>\n          ))}\n        </Row>\n\n        {data?.list.length === 0 && !loading && (\n          <div style={{ textAlign: 'center', padding: '48px', color: '#999' }}>\n            暂无工具\n          </div>\n        )}\n      </Spin>\n\n      {data?.total > 0 && (\n        <div style={{ textAlign: 'center', marginTop: '48px' }}>\n          <Pagination {...pagination} showSizeChanger showQuickJumper showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`} />\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default DisplayIndex;", "__isJSFile": true, "__absFile": "/Users/<USER>/WebstormProjects/inCODE/ui/src/pages/display/index.tsx"}, "admin/Login": {"path": "admin/Login", "id": "admin/Login", "parentId": "@@/global-layout", "file": "admin/Login.tsx", "absPath": "/admin/Login", "__content": "import React from 'react';\nimport { Form, Input, Button, Card, Typography, message, Checkbox } from 'antd';\nimport { UserOutlined, LockOutlined } from '@ant-design/icons';\nimport { history } from 'umi';\nimport { useRequest } from 'ahooks';\n\nconst { Title, Paragraph } = Typography;\n\ninterface LoginForm {\n  username: string;\n  password: string;\n  remember: boolean;\n}\n\n// 1. 封装登录请求服务\n// 这是一个独立的函数，负责调用 API\n// useRequest 会将表单数据作为第一个参数传递给它\nasync function loginService(values: Pick<LoginForm, 'username' | 'password'>) {\n  // 注意：这里我们假设有一个代理或直接可访问的后端服务\n  // 在实际项目中，你可能需要配置 Umi 的代理来解决跨域问题\n  const response = await fetch('http://localhost:8080/api/admin/login', {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    body: JSON.stringify(values),\n  });\n\n  if (!response.ok) {\n    // 如果 HTTP 状态码不是 2xx，也认为是错误\n    const errorData = await response.json().catch(() => ({})); // 尝试解析错误信息\n    throw new Error(errorData.message || '网络请求失败');\n  }\n\n  const data = await response.json();\n\n  if (data.success) {\n    return data; // 成功时，返回整个数据对象\n  } else {\n    // 如果业务上失败，也抛出错误\n    throw new Error(data.message || '登录失败');\n  }\n}\n\nconst AdminLogin: React.FC = () => {\n\n  // 2. 使用 useRequest 管理登录逻辑\n  const { loading, run: runLogin } = useRequest(loginService, {\n    manual: true, // 设置为手动触发，不在组件加载时自动执行\n    onSuccess: (result, params) => {\n      // 登录成功后的回调\n      localStorage.setItem('admin_token', result.token);\n      localStorage.setItem('admin_user', JSON.stringify(result.user));\n      message.success('登录成功！');\n      history.push('/admin/dashboard');\n    },\n    onError: (error) => {\n      // 登录失败后的回调\n      message.error(error.message);\n      history.push('/admin/dashboard'); // 即使失败也跳转，与原逻辑保持一致\n    },\n  });\n\n  // 3. 表单提交时，调用 runLogin 来触发请求\n  const onFinish = (values: LoginForm) => {\n    runLogin({\n      username: values.username,\n      password: values.password,\n    });\n  };\n\n  return (\n    <div style={{\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      padding: '20px'\n    }}>\n      <Card\n        style={{\n          width: '100%',\n          maxWidth: '400px',\n          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',\n          borderRadius: '12px'\n        }}\n      >\n        <div style={{ textAlign: 'center', marginBottom: '32px' }}>\n          <Title level={2} style={{ color: '#1890ff', marginBottom: '8px' }}>\n            管理后台\n          </Title>\n          <Paragraph type=\"secondary\">\n            inCODE 工具链平台管理系统\n          </Paragraph>\n        </div>\n\n        <Form\n          name=\"admin_login\"\n          onFinish={onFinish}\n          autoComplete=\"off\"\n          size=\"large\"\n        >\n          <Form.Item\n            name=\"username\"\n            rules={[\n              { required: true, message: '请输入用户名!' },\n              { min: 3, message: '用户名至少3个字符!' }\n            ]}\n          >\n            <Input\n              prefix={<UserOutlined />}\n              placeholder=\"用户名\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"password\"\n            rules={[\n              { required: true, message: '请输入密码!' },\n              { min: 6, message: '密码至少6个字符!' }\n            ]}\n          >\n            <Input.Password\n              prefix={<LockOutlined />}\n              placeholder=\"密码\"\n            />\n          </Form.Item>\n\n          <Form.Item name=\"remember\" valuePropName=\"checked\">\n            <Checkbox>记住我</Checkbox>\n          </Form.Item>\n\n          <Form.Item>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={loading} // loading 状态由 useRequest 自动管理\n              style={{ width: '100%', height: '44px' }}\n            >\n              登录\n            </Button>\n          </Form.Item>\n        </Form>\n\n        <div style={{ textAlign: 'center', marginTop: '24px' }}>\n          <Paragraph type=\"secondary\" style={{ fontSize: '12px' }}>\n            默认账号：admin / 123456\n          </Paragraph>\n        </div>\n      </Card>\n    </div>\n  );\n};\n\nexport default AdminLogin;", "__isJSFile": true, "__absFile": "/Users/<USER>/WebstormProjects/inCODE/ui/src/pages/admin/Login.tsx"}, "index": {"path": "/", "id": "index", "parentId": "@@/global-layout", "file": "index.tsx", "absPath": "/", "__content": "import { Navigate } from 'umi';\n\nexport default () => {\n  return <Navigate to=\"/display\" replace />;\n};\n", "__isJSFile": true, "__absFile": "/Users/<USER>/WebstormProjects/inCODE/ui/src/pages/index.tsx"}, "@@/global-layout": {"id": "@@/global-layout", "path": "/", "file": "/Users/<USER>/WebstormProjects/inCODE/ui/src/layouts/index.tsx", "absPath": "/", "isLayout": true, "__absFile": "/Users/<USER>/WebstormProjects/inCODE/ui/src/layouts/index.tsx"}}, "apiRoutes": {}, "hasSrcDir": true, "npmClient": "npm", "umi": {"version": "4.4.11", "name": "<PERSON><PERSON>", "importSource": "umi", "cliName": "umi"}, "bundleStatus": {"done": false}, "mfsuBundleStatus": {"done": false}, "react": {"version": "18.3.1", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/react"}, "react-dom": {"version": "18.3.1", "path": "/Users/<USER>/WebstormProjects/inCODE/ui/node_modules/react-dom"}, "appJS": {"path": "/Users/<USER>/WebstormProjects/inCODE/ui/src/app.ts", "exports": ["getInitialState", "layout"]}, "locale": "zh-CN", "globalCSS": [], "globalJS": [], "overridesCSS": [], "bundler": "webpack", "git": {"originUrl": "https://github.com/sangsangde4/inCODE.git"}, "framework": "react", "typescript": {"tsVersion": "5.8.3", "tslibVersion": "2.8.1"}, "faviconFiles": [], "port": 8000, "host": "0.0.0.0", "ip": "*************"}