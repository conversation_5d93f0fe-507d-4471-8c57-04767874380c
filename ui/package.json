{"name": "ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "umi dev", "build": "umi build", "setup": "umi setup", "start": "npm run dev"}, "dependencies": {"@umijs/plugins": "^4.4.2", "ahooks": "^3.8.0", "antd": "^5.21.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^10.1.0", "umi": "^4.4.2"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "typescript": "^5.0.2", "vite": "^4.4.5"}}