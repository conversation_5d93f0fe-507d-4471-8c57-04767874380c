import { defineConfig } from "umi";

export default defineConfig({
  // 注册需要启用的 Umi 插件
  plugins: [
    '@umijs/plugins/dist/antd',
    // '@umijs/plugins/dist/model',
    // '@umijs/plugins/dist/initial-state',
    // '@umijs/plugins/dist/access',
  ],

  // 开启 Vite 模式
  vite: {},
  
  // antd 插件配置
  antd: {},

  // 暂时禁用以下插件配置
  // model: {},
  // initialState: {},
  // access: {},
  
  // 使用约定式路由
  // Umi 会自动扫描 src/pages 目录生成路由

  // 设置 npm 客户端
  npmClient: 'npm',
  
  // 设置路径别名
  alias: {
    '@': '/src',
  }
});